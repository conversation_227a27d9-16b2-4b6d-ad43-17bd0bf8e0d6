# PWA Flow - Sistema de Pedidos

## 🔐 Fluxo Completo Detalhado

### 1. <PERSON><PERSON> inicial via QR Code da mesa
QR Code aponta para:
```
https://pwa.app/c/{establishment}/{location-qr-code}
```

### 2. App solicita geolocalização
- Navegador solicita acesso ao GPS
- **Se negado** → bloqueia o fluxo com aviso
- **Se aceito** → envia:
```json
{
  "establishment_id": 123,
  "location_id": 456,
  "latitude": -23.5505,
  "longitude": -46.6333
}
```

### 3. Backend valida a localização e emite token de sessão curto
- Verifica se está dentro do raio definido (ex: 30–50 metros)
- Retorna:
```json
{
  "session_token": "sess-abc123",
  "expires_at": "2025-05-31T14:32:00Z"
}
```

### 4. App permite navegar, montar pedido etc.
- Sessão está ativa
- App armazena o `session_token` no sessionStorage
- Usuário navega pelo menu e monta o pedido

### 5. Ao clicar em "Confirmar Pedido", o app solicita escaneamento do QR Code
- Abre câmera com biblioteca QR
- Espera leitura do QR code da mesa (ex: `mesa-12-abc123`)

### 6. App envia o pedido com:
```json
{
  "session_token": "sess-abc123",
  "qr_code_scanned": "mesa-12-abc123",
  "customer_name": "João Silva",
  "special_requests": "Sem cebola",
  "items": [
    {
      "type": "dish",
      "id": 1,
      "quantity": 2
    }
  ]
}
```

### 7. Backend valida:
- Se `session_token` é válido e dentro do tempo
- Se `location.qr_code` da sessão bate com o `qr_code_scanned`
- Se sim → processa pedido

## 🔧 Implementação Técnica

### Rotas
- **Acesso inicial**: `/c/{establishment:slug}/{location:qr_code}`
- **Start session**: `POST /api/customer/session/start`
- **Place order**: `POST /api/customer/order/place`

### Session Storage
```javascript
sessionStorage.setItem('pwa_session_token', token);
sessionStorage.setItem('pwa_session_expiry', expiry);
```

### Validações
1. **Geolocalização**: Raio de 500m do estabelecimento
2. **Session TTL**: 5 minutos de duração
3. **QR Match**: QR escaneado deve bater com location.qr_code da sessão

## 🚨 Pontos Críticos

1. **Sessão deve ser iniciada APENAS no acesso inicial via QR**
2. **Geolocalização é obrigatória para criar sessão**
3. **QR re-scan é obrigatório para finalizar pedido**
4. **Token expira em 5 minutos - deve ser renovado se necessário**
5. **Validação dupla: sessão + QR match**
