<?php

namespace App\Policies;

use App\Models\Establishment;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class EstablishmentPolicy
{
    /**
     * Determine if the user can access the establishment.
     */
    public function access(User $user, Establishment $establishment): Response
    {
        // Super admin tem acesso a todos os estabelecimentos
        if ($user->isSuperAdmin()) {
            return Response::allow();
        }

        // Verifica se o usuário é admin do estabelecimento
        if ($user->isAdmin() && $user->establishments->contains($establishment)) {
            return Response::allow();
        }

        return Response::deny('Você não tem permissão para acessar este estabelecimento.');
    }

    /**
     * Determine if the user can create models.
     */
    public function create(User $user): bool
    {
        // Apenas super admins podem criar estabelecimentos
        return $user->isSuperAdmin();
    }

    /**
     * Determine if the user can update the model.
     */
    public function update(User $user, Establishment $establishment): bool
    {
        // Super admin pode atualizar qualquer estabelecimento
        if ($user->isSuperAdmin()) {
            return true;
        }

        // Admin só pode atualizar seus próprios estabelecimentos
        return $user->isAdmin() && $user->establishments->contains($establishment);
    }

    /**
     * Determine if the user can delete the model.
     */
    public function delete(User $user, Establishment $establishment): bool
    {
        // Apenas super admins podem deletar estabelecimentos
        return $user->isSuperAdmin();
    }
}
