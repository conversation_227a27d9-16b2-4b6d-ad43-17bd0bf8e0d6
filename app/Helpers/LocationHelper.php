<?php

namespace App\Helpers;

class LocationHelper
{
    /**
     * Retorna o artigo definido (o/a) baseado no tipo de localização
     *
     * @param string $type Tipo de localização (room, table, etc)
     * @return string
     */
    public static function getArticle($type)
    {
        return in_array($type, ['room']) ? 'o' : 'a';
    }

    /**
     * Retorna o artigo definido com preposição (no/na) baseado no tipo de localização
     *
     * @param string $type Tipo de localização (room, table, etc)
     * @return string
     */
    public static function getPrepositionArticle($type)
    {
        return in_array($type, ['room']) ? 'no' : 'na';
    }

    /**
     * Retorna o adjetivo (novo/nova) baseado no tipo de localização
     *
     * @param string $type Tipo de localização (room, table, etc)
     * @return string
     */
    public static function getNewAdjective($type)
    {
        return in_array($type, ['room']) ? 'Novo' : 'Nova';
    }
}