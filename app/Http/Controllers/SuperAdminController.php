<?php

namespace App\Http\Controllers;

use App\Models\Establishment;
use App\Models\User;
use App\Models\Order;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class SuperAdminController extends Controller
{
    public function __construct()
    {

    }

    public function dashboard(): View|Application|Factory
    {
        $totalEstablishments = Establishment::count();
        $activeEstablishments = Establishment::where('active', true)->count();
        $totalUsers = User::where('role', '!=', 'super_admin')->count();
        $totalOrders = Order::count();
        $todayOrders = Order::whereDate('created_at', today())->count();
        $totalRevenue = Order::where('status', 'delivered')->sum('total_amount');

        $recentEstablishments = Establishment::with('users')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $establishmentsByType = Establishment::selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->get();

        return view('super-admin.dashboard', compact(
            'totalEstablishments',
            'activeEstablishments',
            'totalUsers',
            'totalOrders',
            'todayOrders',
            'totalRevenue',
            'recentEstablishments',
            'establishmentsByType'
        ));
    }

    public function establishments(): View|Application|Factory
    {
        $establishments = Establishment::with('users')
            ->withCount(['locations', 'dishes', 'services', 'orders'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('super-admin.establishments.index', compact('establishments'));
    }

    public function createEstablishment(): View|Application|Factory
    {
        return view('super-admin.establishments.create');
    }

    public function storeEstablishment(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:hotel,restaurant,cafe,bar,food_truck',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'logo_url' => 'nullable|url',
            'slug' => 'nullable|string|unique:establishments,slug',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'owner_name' => 'required|string|max:255',
            'owner_email' => 'required|email|unique:users,email',
            'owner_password' => 'required|string|min:8',
        ]);

        // Criar o estabelecimento
        $establishment = Establishment::create([
            'name' => $validated['name'],
            'slug' => $validated['slug'] ?? Str::slug($validated['name']),
            'type' => $validated['type'],
            'description' => $validated['description'],
            'address' => $validated['address'],
            'latitude' => $validated['latitude'],
            'longitude' => $validated['longitude'],
            'phone' => $validated['phone'],
            'email' => $validated['email'],
            'logo_url' => $validated['logo_url'],
            'active' => true,
        ]);

        // Criar o usuário proprietário
        $owner = User::create([
            'name' => $validated['owner_name'],
            'email' => $validated['owner_email'],
            'password' => Hash::make($validated['owner_password']),
            'role' => 'user',
        ]);

        // Associar o proprietário ao estabelecimento
        $establishment->users()->attach($owner->id, ['role' => 'owner']);

        return redirect()->route('super-admin.establishments.index')
            ->with('success', 'Estabelecimento criado com sucesso!');
    }

    public function showEstablishment(Establishment $establishment): View|Application|Factory
    {
        $establishment->load(['users', 'locations', 'dishes', 'services']);
        $establishment->loadCount(['orders', 'locations', 'dishes', 'services']);

        $recentOrders = $establishment->orders()
            ->with('location')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $monthlyRevenue = $establishment->orders()
            ->where('status', 'delivered')
            ->whereMonth('created_at', now()->month)
            ->sum('total_amount');

        return view('super-admin.establishments.show', compact('establishment', 'recentOrders', 'monthlyRevenue'));
    }

    public function editEstablishment(Establishment $establishment): View|Application|Factory
    {
        $establishment->load('users');
        return view('super-admin.establishments.edit', compact('establishment'));
    }

    public function updateEstablishment(Request $request, Establishment $establishment): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:hotel,restaurant,cafe,bar,food_truck',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'logo_url' => 'nullable|url',
            'slug' => 'nullable|string|unique:establishments,slug,'.$establishment->id,
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'active' => 'boolean',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $establishment->update($validated);

        return redirect()->route('super-admin.establishments.show', $establishment)
            ->with('success', 'Estabelecimento atualizado com sucesso!');
    }

    public function destroyEstablishment(Establishment $establishment): RedirectResponse
    {
        $establishment->delete();

        return redirect()->route('super-admin.establishments.index')
            ->with('success', 'Estabelecimento excluído com sucesso!');
    }


    public function toggleEstablishmentStatus(Establishment $establishment): JsonResponse
    {
        try {
            $establishment->update(['active' => !$establishment->active]);
            $newStatus = $establishment->active ? 'Ativo' : 'Inativo';

            return response()->json([
                'success' => true,
                'message' => 'Status alterado com sucesso!',
                'newStatus' => $newStatus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ocorreu um erro ao alterar o status.'
            ], 500);
        }
    }

    public function analytics(): View|Application|Factory
    {
        // Dados para gráficos e relatórios
        $establishmentsByType = Establishment::selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->get();

        $ordersByMonth = Order::selectRaw('MONTH(created_at) as month, COUNT(*) as count, SUM(total_amount) as revenue')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $topEstablishments = Establishment::withSum('orders', 'total_amount')
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit(10)
            ->get();

        return view('super-admin.analytics', compact(
            'establishmentsByType',
            'ordersByMonth',
            'topEstablishments'
        ));
    }

    /**
     * Exibe a lista de usuários.
     */
    public function users(): View|Application|Factory
    {
        $users = User::where('role', '!=', 'super_admin')
            ->with(['establishments'])
            ->orderBy('name')
            ->paginate(20);

        return view('super-admin.users.index', compact('users'));
    }

    /**
     * Mostra o formulário de criação de usuário.
     */
    public function createUser(): View|Application|Factory
    {
        $establishments = Establishment::active()->orderBy('name')->get();
        return view('super-admin.users.create', compact('establishments'));
    }

    /**
     * Armazena um novo usuário.
     */
    public function storeUser(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', 'min:8'],
            'role' => ['required', 'in:admin,user'],
            'establishments' => ['nullable', 'array'],
            'establishments.*' => ['exists:establishments,id'],
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
        ]);

        // Associa o usuário aos estabelecimentos selecionados
        if (!empty($validated['establishments'])) {
            $user->establishments()->sync($validated['establishments']);
        }

        return redirect()->route('super-admin.users.index')
            ->with('success', 'Usuário criado com sucesso!');
    }

    /**
     * Mostra o formulário de edição de usuário.
     */
    public function editUser(User $user): View|Application|Factory
    {
        if ($user->isSuperAdmin()) {
            abort(403, 'Não é possível editar um super administrador.');
        }

        $establishments = Establishment::active()->orderBy('name')->get();
        $userEstablishments = $user->establishments->pluck('id')->toArray();

        return view('super-admin.users.edit', compact('user', 'establishments', 'userEstablishments'));
    }

    /**
     * Atualiza um usuário existente.
     */
    public function updateUser(Request $request, User $user): RedirectResponse
    {
        if ($user->isSuperAdmin()) {
            abort(403, 'Não é possível editar um super administrador.');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', 'min:8'],
            'role' => ['required', 'in:admin,user'],
            'establishments' => ['nullable', 'array'],
            'establishments.*' => ['exists:establishments,id'],
        ]);

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'role' => $validated['role'],
        ];

        if (!empty($validated['password'])) {
            $updateData['password'] = Hash::make($validated['password']);
        }

        $user->update($updateData);

        // Atualiza os estabelecimentos do usuário
        $user->establishments()->sync($validated['establishments'] ?? []);

        return redirect()->route('super-admin.users.index')
            ->with('success', 'Usuário atualizado com sucesso!');
    }

    /**
     * Remove um usuário.
     */
    public function destroyUser(User $user): RedirectResponse
    {
        if ($user->isSuperAdmin()) {
            abort(403, 'Não é possível remover um super administrador.');
        }

        // Remove as associações com estabelecimentos
        $user->establishments()->detach();

        // Remove o usuário
        $user->delete();

        return redirect()->route('super-admin.users.index')
            ->with('success', 'Usuário removido com sucesso!');
    }
}
