<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;

abstract class Controller
{
    /**
     * Get available dish categories
     *
     * @return array
     */
    protected function getDishCategories(): array
    {
        $dishes = [
            'Entradas',
            '<PERSON><PERSON><PERSON>rin<PERSON>',
            '<PERSON><PERSON><PERSON>as',
            '<PERSON><PERSON><PERSON>',
            'Lanches',
            'Saladas',
            '<PERSON>as',
            'Carnes',
            'Peixes',
            'Vegetariano',
            'Vegano'
        ];

        return array_map(function (string $dish) {
            $dishObj = new \StdClass();

            $dishObj->name = $dishObj;
            $dishObj->slug = Str::slug($dish);
            $dishObj->type = 'dish';
            $dishObj->image = 'https://placehold.co/300x300';

            return $dishObj;

        }, $dishes);
    }

    protected function getServiceCategories(): array
    {
        $services = [
            'Limpeza',
            'Room Service',
            'Spa',
            'Massagem',
            'Sauna',
            'Terraquênia',
            'Piscina',
            'Academia',
            'Bar',
            'Restaurante'
        ];

        return array_map(function (string $service) {
            $serviceObj = new \StdClass();
            $serviceObj->name = $service;
            $serviceObj->slug = Str::slug($service);
            $serviceObj->type = 'service';
            $serviceObj->image = 'https://placehold.co/300x300';

            return $serviceObj;

        }, $services);
    }
}
