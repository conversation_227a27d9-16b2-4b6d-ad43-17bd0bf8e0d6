<?php

namespace App\Http\Controllers;

use App\Models\Establishment;
use App\Models\Location;
use App\Models\Dish;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EstablishmentController extends Controller
{
    public function __construct()
    {

    }

    public function dashboard($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        $pendingOrders = $establishment->orders()->where('status', 'pending')->count();
        $occupiedLocations = $establishment->locations()->where('status', 'occupied')->count();
        $todayRevenue = $establishment->orders()
            ->whereDate('created_at', today())
            ->where('status', 'delivered')
            ->sum('total_amount');

        $todayOrders = $establishment->orders()->whereDate('created_at', today())->count();
        $totalLocations = $establishment->locations()->count();
        $totalDishes = $establishment->dishes()->count();

        // Pedidos recentes
        $recentOrders = $establishment->orders()
            ->with(['location', 'items.dish', 'items.service'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Pratos mais pedidos
        $topDishes = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('dishes', 'order_items.dish_id', '=', 'dishes.id')
            ->where('orders.establishment_id', $establishmentId)
            ->whereNotNull('order_items.dish_id')
            ->select('dishes.name', DB::raw('SUM(order_items.quantity) as total_quantity'))
            ->groupBy('dishes.id', 'dishes.name')
            ->orderBy('total_quantity', 'desc')
            ->limit(5)
            ->get();

        return view('establishment.dashboard', compact(
            'establishment',
            'pendingOrders',
            'occupiedLocations',
            'todayRevenue',
            'todayOrders',
            'totalLocations',
            'totalDishes',
            'recentOrders',
            'topDishes'
        ));
    }

    public function orders($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        $orders = $establishment->orders()
            ->with(['location', 'items.dish', 'items.service'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('establishment.orders.index', compact('establishment', 'orders'));
    }

    public function updateOrderStatus(Request $request, $establishmentId, Order $order)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,preparing,ready,delivered,cancelled'
        ]);

        // Verificar se o pedido pertence ao estabelecimento
        if ($order->establishment_id != $establishmentId) {
            abort(403);
        }

        $order->update(['status' => $validated['status']]);

        return response()->json(['success' => true]);
    }

    public function sendOrderNotification(Request $request, $establishmentId, Order $order)
    {
        $validated = $request->validate([
            'message' => 'required|string|max:500'
        ]);

        // Verificar se o pedido pertence ao estabelecimento
        if ($order->establishment_id != $establishmentId) {
            abort(403);
        }

        // Aqui você implementaria o envio da notificação
        // Por exemplo, via SMS, WhatsApp, email, etc.

        // Simular envio de notificação
        $notification = [
            'order_id' => $order->id,
            'customer_name' => $order->customer_name,
            'message' => $validated['message'],
            'sent_at' => now()
        ];

        // Salvar log da notificação (você pode criar uma tabela para isso)

        return response()->json([
            'success' => true,
            'message' => 'Notificação enviada com sucesso!'
        ]);
    }

    public function locations($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        $locations = $establishment->locations()->orderBy('identifier')->paginate(20);

        return view('establishment.locations.index', compact('establishment', 'locations'));
    }

    public function createLocation($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        return view('establishment.locations.create', compact('establishment'));
    }

    public function storeLocation(Request $request, $establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        $validated = $request->validate([
            'identifier' => 'required|string',
            'name' => 'nullable|string|max:255',
            'type' => 'required|in:room,table,counter,booth',
            'capacity' => 'nullable|integer|min:1',
            'floor' => 'nullable|integer',
            'section' => 'nullable|string|max:100',
        ]);

        // Verificar se o identificador já existe neste estabelecimento
        if ($establishment->locations()->where('identifier', $validated['identifier'])->exists()) {
            return back()->withErrors(['identifier' => 'Este identificador já existe neste estabelecimento.']);
        }

        $location = $establishment->locations()->create($validated);
        $location->generateQRCode();

        return redirect()->route('establishment.locations.index', $establishmentId)
            ->with('success', ucfirst($establishment->location_type) . ' criado(a) com sucesso!');
    }

    public function editLocation($establishmentId, Location $location)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        if ($location->establishment_id != $establishmentId) {
            abort(403);
        }

        return view('establishment.locations.edit', compact('establishment', 'location'));
    }

    public function updateLocation(Request $request, $establishmentId, Location $location)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        if ($location->establishment_id != $establishmentId) {
            abort(403);
        }

        $validated = $request->validate([
            'identifier' => 'required|string',
            'name' => 'nullable|string|max:255',
            'type' => 'required|in:room,table,counter,booth',
            'capacity' => 'nullable|integer|min:1',
            'floor' => 'nullable|integer',
            'section' => 'nullable|string|max:100',
            'status' => 'required|in:available,occupied,maintenance,reserved',
            'customer_name' => 'nullable|string|max:255',
        ]);

        // Verificar se o identificador já existe (exceto o atual)
        if ($establishment->locations()
            ->where('identifier', $validated['identifier'])
            ->where('id', '!=', $location->id)
            ->exists()) {
            return back()->withErrors(['identifier' => 'Este identificador já existe neste estabelecimento.']);
        }

        $location->update($validated);

        return redirect()->route('establishment.locations.index', $establishmentId)
            ->with('success', ucfirst($establishment->location_type) . ' atualizado(a) com sucesso!');
    }

    public function destroyLocation($establishmentId, Location $location)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        if ($location->establishment_id != $establishmentId) {
            abort(403);
        }

        $location->delete();

        return redirect()->route('establishment.locations.index', $establishmentId)
            ->with('success', ucfirst($establishment->location_type) . ' excluído(a) com sucesso!');
    }

    public function dishes($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        $dishes = $establishment->dishes()->orderBy('category')->paginate(20);

        return view('establishment.dishes.index', compact('establishment', 'dishes'));
    }

    public function createDish($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        return view('establishment.dishes.create', compact('establishment'));
    }

    public function storeDish(Request $request, $establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        // Processar dados JSON dos arrays
        $ingredients = $request->input('ingredients');
        $dietaryRestrictions = $request->input('dietary_restrictions');
        $customizableOptions = $request->input('customizable_options');

        // Decodificar JSON se necessário
        if (is_string($ingredients)) {
            $ingredients = json_decode($ingredients, true) ?: [];
        }
        if (is_string($dietaryRestrictions)) {
            $dietaryRestrictions = json_decode($dietaryRestrictions, true) ?: [];
        }
        if (is_string($customizableOptions)) {
            $customizableOptions = json_decode($customizableOptions, true) ?: [];
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category' => 'required|string|max:100',
            'image_url' => 'nullable|url',
        ]);

        // Validar arrays manualmente
        if (empty($ingredients) || !is_array($ingredients)) {
            return back()->withErrors(['ingredients' => 'Pelo menos um ingrediente é obrigatório.'])->withInput();
        }

        // Adicionar arrays processados aos dados validados
        $validated['ingredients'] = $ingredients;
        $validated['dietary_restrictions'] = $dietaryRestrictions;
        $validated['customizable_options'] = $customizableOptions;

        $establishment->dishes()->create($validated);

        return redirect()->route('establishment.dishes.index', $establishmentId)
            ->with('success', 'Prato criado com sucesso!');
    }

    public function editDish($establishmentId, Dish $dish)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        if ($dish->establishment_id != $establishmentId) {
            abort(403);
        }

        return view('establishment.dishes.edit', compact('establishment', 'dish'));
    }

    public function updateDish(Request $request, $establishmentId, Dish $dish)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        if ($dish->establishment_id != $establishmentId) {
            abort(403);
        }

        // Processar dados JSON dos arrays
        $ingredients = $request->input('ingredients');
        $dietaryRestrictions = $request->input('dietary_restrictions');
        $customizableOptions = $request->input('customizable_options');

        // Decodificar JSON se necessário
        if (is_string($ingredients)) {
            $ingredients = json_decode($ingredients, true) ?: [];
        }
        if (is_string($dietaryRestrictions)) {
            $dietaryRestrictions = json_decode($dietaryRestrictions, true) ?: [];
        }
        if (is_string($customizableOptions)) {
            $customizableOptions = json_decode($customizableOptions, true) ?: [];
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category' => 'required|string|max:100',
            'image_url' => 'nullable|url',
            'available' => 'boolean',
        ]);

        // Validar arrays manualmente
        if (empty($ingredients) || !is_array($ingredients)) {
            return back()->withErrors(['ingredients' => 'Pelo menos um ingrediente é obrigatório.'])->withInput();
        }

        // Adicionar arrays processados aos dados validados
        $validated['ingredients'] = $ingredients;
        $validated['dietary_restrictions'] = $dietaryRestrictions;
        $validated['customizable_options'] = $customizableOptions;

        // Garantir que available seja boolean
        $validated['available'] = $request->has('available') ? true : false;

        $dish->update($validated);

        return redirect()->route('establishment.dishes.index', $establishmentId)
            ->with('success', 'Prato atualizado com sucesso!');
    }

    public function destroyDish($establishmentId, Dish $dish)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        if ($dish->establishment_id != $establishmentId) {
            abort(403);
        }

        $dish->delete();

        return redirect()->route('establishment.dishes.index', $establishmentId)
            ->with('success', 'Prato excluído com sucesso!');
    }

    public function services($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        $services = $establishment->services()->orderBy('category')->paginate(20);

        return view('establishment.services.index', compact('establishment', 'services'));
    }

    public function createService($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        return view('establishment.services.create', compact('establishment'));
    }

    public function storeService(Request $request, $establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'price' => 'nullable|numeric|min:0',
            'duration' => 'nullable|integer|min:1',
        ]);

        $establishment->services()->create($validated);

        return redirect()->route('establishment.services.index', $establishmentId)
            ->with('success', 'Serviço criado com sucesso!');
    }

    public function showQRCode($establishmentId, $locationId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        $location = $establishment->locations()->findOrFail($locationId);

        return view('establishment.locations.qrcode', compact('establishment', 'location'));
    }

    public function analytics($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        // Dados para gráficos dos últimos 30 dias
        $ordersByDay = $establishment->orders()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(total_amount) as revenue')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Pedidos por status
        $ordersByStatus = $establishment->orders()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Pratos mais vendidos
        $topDishes = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('dishes', 'order_items.dish_id', '=', 'dishes.id')
            ->where('orders.establishment_id', $establishmentId)
            ->whereNotNull('order_items.dish_id')
            ->select('dishes.name', 'dishes.price', DB::raw('SUM(order_items.quantity) as total_quantity'), DB::raw('SUM(order_items.quantity * order_items.unit_price) as total_revenue'))
            ->groupBy('dishes.id', 'dishes.name', 'dishes.price')
            ->orderBy('total_quantity', 'desc')
            ->limit(10)
            ->get();

        // Receita por hora do dia
        $revenueByHour = $establishment->orders()
            ->selectRaw('HOUR(created_at) as hour, SUM(total_amount) as revenue, COUNT(*) as orders_count')
            ->where('created_at', '>=', now()->subDays(7))
            ->where('status', 'delivered')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        // Locais mais utilizados
        $locationUsage = $establishment->orders()
            ->join('locations', 'orders.location_id', '=', 'locations.id')
            ->selectRaw('locations.identifier, locations.name, COUNT(*) as orders_count, SUM(orders.total_amount) as total_revenue')
            ->groupBy('locations.id', 'locations.identifier', 'locations.name')
            ->orderBy('orders_count', 'desc')
            ->limit(10)
            ->get();

        // Métricas gerais
        $totalRevenue = $establishment->orders()->where('status', 'delivered')->sum('total_amount');
        $totalOrders = $establishment->orders()->count();
        $averageOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;
        $monthlyRevenue = $establishment->orders()
            ->where('status', 'delivered')
            ->whereMonth('created_at', now()->month)
            ->sum('total_amount');

        return view('establishment.analytics', compact(
            'establishment',
            'ordersByDay',
            'ordersByStatus',
            'topDishes',
            'revenueByHour',
            'locationUsage',
            'totalRevenue',
            'totalOrders',
            'averageOrderValue',
            'monthlyRevenue'
        ));
    }

    public function settings($establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);
        return view('establishment.settings', compact('establishment'));
    }

    public function updateSettings(Request $request, $establishmentId)
    {
        $establishment = Establishment::findOrFail($establishmentId);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'logo_url' => 'nullable|url',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'delivery_radius' => 'nullable|integer|min:10|max:5000',
            'delivery_time' => 'nullable|integer|min:5|max:120',
            'theme_color' => 'nullable|string|max:7',
            'notifications_enabled' => 'boolean',
            'auto_accept_orders' => 'boolean',
        ]);

        // Atualizar configurações no campo settings
        $settings = $establishment->settings ?? [];
        $settings['latitude'] = $validated['latitude'] ?? null;
        $settings['longitude'] = $validated['longitude'] ?? null;
        $settings['delivery_radius'] = $validated['delivery_radius'] ?? 500; // 500m padrão
        $settings['delivery_time'] = $validated['delivery_time'] ?? 30;
        $settings['theme_color'] = $validated['theme_color'] ?? '#007bff';
        $settings['notifications_enabled'] = $validated['notifications_enabled'] ?? false;
        $settings['auto_accept_orders'] = $validated['auto_accept_orders'] ?? false;

        $establishment->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'address' => $validated['address'],
            'phone' => $validated['phone'],
            'email' => $validated['email'],
            'logo_url' => $validated['logo_url'],
            'settings' => $settings,
        ]);

        return redirect()->route('establishment.settings', $establishmentId)
            ->with('success', 'Configurações atualizadas com sucesso!');
    }
}
