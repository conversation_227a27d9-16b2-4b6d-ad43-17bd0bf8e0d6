<?php

namespace App\Http\Controllers;

use App\Models\Room;
use App\Models\Dish;
use App\Models\Service;
use App\Models\Order;
use Illuminate\Http\Request;

class HostController extends Controller
{
    public function dashboard()
    {
        $pendingOrders = Order::where('status', 'pendente')->count();
        $activeRooms = Room::where('status', 'ocupado')->count();
        $todayRevenue = Order::whereDate('created_at', today())
            ->where('status', 'concluido')
            ->sum('total_amount');

        return view('host.dashboard', compact('pendingOrders', 'activeRooms', 'todayRevenue'));
    }

    public function orders()
    {
        $orders = Order::with(['room', 'items.dish', 'items.service'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('host.orders.index', compact('orders'));
    }

    public function updateOrderStatus(Request $request, Order $order)
    {
        $validated = $request->validate([
            'status' => 'required|in:pendente,preparando,a_caminho,concluido,cancelado'
        ]);

        $order->update(['status' => $validated['status']]);

        return response()->json(['success' => true]);
    }

    public function dishes()
    {
        $dishes = Dish::orderBy('category')->paginate(20);
        return view('host.dishes.index', compact('dishes'));
    }

    public function createDish()
    {
        return view('host.dishes.create');
    }

    public function storeDish(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category' => 'required|string|max:100',
            'ingredients' => 'required|array',
            'dietary_restrictions' => 'nullable|array',
            'customizable_options' => 'nullable|array',
            'image_url' => 'nullable|url',
        ]);

        Dish::create($validated);

        return redirect()->route('host.dishes.index')
            ->with('success', 'Prato criado com sucesso!');
    }

    public function rooms()
    {
        $rooms = Room::orderBy('number')->paginate(20);
        return view('host.rooms.index', compact('rooms'));
    }

    public function createRoom()
    {
        return view('host.rooms.create');
    }

    public function storeRoom(Request $request)
    {
        $validated = $request->validate([
            'number' => 'required|string|unique:rooms',
            'floor' => 'required|integer',
            'type' => 'required|string|max:100',
            'guest_name' => 'nullable|string|max:255',
            'check_in' => 'nullable|date',
            'check_out' => 'nullable|date|after:check_in',
        ]);

        $room = Room::create(array_merge($validated, [
            'status' => $validated['guest_name'] ? 'ocupado' : 'disponivel'
        ]));

        $room->generateQRCode();

        return redirect()->route('host.rooms.index')
            ->with('success', 'Quarto criado com sucesso!');
    }
}
