<?php

namespace App\Http\Controllers;

use App\Models\Establishment;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class BaseEstablishmentController extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;


    /**
     * Criar uma nova instância do controlador
     *
     * @param Establishment $establishment
     * @return void
     */
    public function __construct(private readonly Establishment $establishment)
    {
        $this->middleware(function ($request, $next) use ($establishment) {
            $this->establishment = $establishment;

            // Compartilhar o estabelecimento com todas as views
            view()->share('establishment', $establishment);

            return $next($request);
        });
    }

    /**
     * Obter o estabelecimento atual
     *
     * @return Establishment
     */
    protected function getEstablishment()
    {
        return $this->establishment;
    }
}
