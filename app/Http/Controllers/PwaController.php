<?php

namespace App\Http\Controllers;

use App\Models\Establishment;
use App\Models\Location;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class PwaController extends Controller
{
    /**
     * Display the main menu for a specific location within an establishment.
     * This is the entry point from the physical QR Code.
     */
    public function showMenu(Establishment $establishment, Location $location): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        $mostSoldDishes = $establishment->dishes()->where('available', true)->limit(3)->get();

        $dishes = $establishment->dishes()->where('available', true)->get();
        $services = $establishment->services()->where('available', true)->get()->groupBy('category');

        $showCartButton = true;

        return view('customer.pwa-menu', compact('location', 'establishment', 'dishes', 'services', 'mostSoldDishes', 'showCartButton'));
    }

    /**
     * Start a short-lived PWA session after validating the user's geolocation.
     */
    public function startSession(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'establishment_id' => 'required|exists:establishments,id',
            'location_id' => 'required|exists:locations,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $establishment = Establishment::findOrFail($validated['establishment_id']);

        // Check if the establishment has coordinates configured
        if (!$establishment->latitude || !$establishment->longitude) {
            // If not, we can't geovalidate, so we might allow access but without location check.
            // For now, let's assume it's mandatory.
             return response()->json(['error' => 'Establishment location not configured.'], 422);
        }

        $distance = $this->calculateDistance(
            $validated['latitude'],
            $validated['longitude'],
            $establishment->latitude,
            $establishment->longitude
        );

        // Allow a radius of 500 meters for now. This should be a setting later.
        $allowedRadius = 500;

        if ($distance > $allowedRadius) {
            return response()->json([
                'error' => 'You are too far from the establishment to place an order.'
            ], 403);
        }

        // Create a short-lived session token
        $sessionToken = Str::random(40);
        $expiresAt = now()->addMinutes(5);

        Cache::put('pwa-session-'.$sessionToken, [
            'establishment_id' => $establishment->id,
            'location_id' => $validated['location_id'],
        ], $expiresAt);

        return response()->json([
            'session_token' => $sessionToken,
            'expires_at' => $expiresAt->toIso8601String(),
        ]);
    }

    /**
     * Show items from a specific category
     */
    public function showCategory(Establishment $establishment, Location $location, $type, $category): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        if ($type === 'dish') {
            $items = $establishment->dishes()
                ->where('available', true)
                ->where('category', $category)
                ->get();
        } elseif ($type === 'service') {
            $items = $establishment->services()
                ->where('available', true)
                ->where('category', $category)
                ->get();
        } else {
            abort(404, 'Invalid type.');
        }

        $showBackButton = true;
        return view('customer.pwa-category', compact('location', 'establishment', 'items', 'category', 'type', 'showBackButton'));
    }

    /**
     * Show cart/order page
     */
    public function showCart(Establishment $establishment, Location $location): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        $showBackButton = true;
        return view('customer.pwa-cart', compact('location', 'establishment', 'showBackButton'));
    }

    /**
     * Show QR scanner page
     */
    public function showQRScanner(Establishment $establishment, Location $location): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        $showBackButton = true;
        return view('customer.pwa-qr-scanner', compact('location', 'establishment', 'showBackButton'));
    }

    /**
     * Show virtual waiter chat
     */
    public function showChat(Establishment $establishment, Location $location): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        $showBackButton = true;
        return view('customer.pwa-chat', compact('location', 'establishment', 'showBackButton'));
    }

    /**
     * Show item details (dish or service)
     */
    public function showItemDetails(Establishment $establishment, Location $location, $type, $id): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        if ($type === 'dish') {
            $item = $establishment->dishes()->where('available', true)->findOrFail($id);
        } elseif ($type === 'service') {
            $item = $establishment->services()->where('available', true)->findOrFail($id);
        } else {
            abort(404, 'Invalid item type.');
        }

        return view('customer.pwa-item-details', [
            'establishment' => $establishment,
            'showBackButton' => true,
            'showCartButton' => true,
            'location' => $location,
            'type' => $type,
            'item' => $item
        ]);
    }

    /**
     * Show order confirmation page
     */
    public function showOrderConfirmation(Establishment $establishment, Location $location, $orderId = null): View|Application|Factory
    {
        // Ensure the location belongs to the establishment from the URL
        if ($location->establishment_id !== $establishment->id) {
            abort(404, 'Location not found for this establishment.');
        }

        $order = null;
        if ($orderId) {
            $order = Order::with(['items.dish', 'items.service'])
                ->where('establishment_id', $establishment->id)
                ->where('location_id', $location->id)
                ->findOrFail($orderId);
        }

        $showBackButton = true;
        return view('customer.pwa-order-confirmation', compact('location', 'establishment', 'order', 'showBackButton'));
    }

    /**
     * Place a new order after validating the session and the QR code re-scan.
     */
    public function placeOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'session_token' => 'required|string',
            'qr_code_scanned' => 'required|string',
            'customer_name' => 'required|string|max:255',
            'items' => 'required|array|min:1',
            'items.*.type' => 'required|in:dish,service',
            'items.*.id' => 'required|integer',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        // 1. Validate Session Token
        $sessionData = Cache::get('pwa-session-'.$validated['session_token']);
        if (!$sessionData) {
            throw ValidationException::withMessages(['session_token' => 'Your session has expired. Please refresh the page.']);
        }

        // 2. Validate QR Code Rescan
        $location = Location::findOrFail($sessionData['location_id']);
        if ($location->qr_code !== $validated['qr_code_scanned']) {
             return response()->json(['error' => 'QR Code scanned does not match the table. Please try again.'], 422);
        }

        // 3. Create the Order
        $order = Order::create([
            'establishment_id' => $location->establishment_id,
            'location_id' => $location->id,
            'customer_name' => $validated['customer_name'],
            'status' => 'pending',
        ]);

        // 4. Add items to the order
        foreach ($validated['items'] as $item) {
            $model = $item['type'] === 'dish' ? \App\Models\Dish::class : \App\Models\Service::class;
            $resource = $model::where('establishment_id', $location->establishment_id)->findOrFail($item['id']);

            OrderItem::create([
                'order_id' => $order->id,
                'dish_id' => $item['type'] === 'dish' ? $resource->id : null,
                'service_id' => $item['type'] === 'service' ? $resource->id : null,
                'quantity' => $item['quantity'],
                'unit_price' => $resource->price,
            ]);
        }
        $order->calculateTotal();

        // 5. Invalidate the session token
        Cache::forget('pwa-session-'.$validated['session_token']);

        return response()->json([
            'success' => true,
            'order_id' => $order->id,
            'message' => 'Order placed successfully!'
        ]);
    }

    /**
     * Calculates the distance between two points on Earth.
     * @return float Distance in meters.
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371000; // in meters

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}
