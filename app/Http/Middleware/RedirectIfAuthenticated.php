<?php

namespace App\Http\Middleware;

use App\Models\Establishment;
use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $user = Auth::user();
                
                // Redireciona para o painel apropriado com base no papel do usuário
                if ($user->isSuperAdmin()) {
                    return redirect()->route('super-admin.dashboard');
                }
                
                if ($user->isAdmin()) {
                    $establishment = $user->establishments()->first();
                    
                    if ($establishment) {
                        return redirect()->route('establishment.dashboard', $establishment);
                    }
                }
                
                // Se for um usuário comum ou não tiver estabelecimento, redireciona para a página inicial
                return redirect(RouteServiceProvider::HOME);
            }
        }

        return $next($request);
    }
}
