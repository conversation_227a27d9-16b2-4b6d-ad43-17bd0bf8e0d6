<?php

namespace App\Http\Middleware;

use App\Models\Establishment;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        // Se não estiver autenticado, redireciona para o login
        if (!$user) {
            return redirect()->route('login');
        }
        
        // Se for super admin, permite o acesso
        if ($user->isSuperAdmin()) {
            return $next($request);
        }
        
        // Se for admin, verifica se tem acesso ao estabelecimento
        if ($user->isAdmin()) {
            $establishmentId = $request->route('establishmentId');
            
            // Se não houver establishmentId, permite o acesso (pode ser uma rota sem parâmetro de estabelecimento)
            if (!$establishmentId) {
                return $next($request);
            }
            
            // Verifica se o usuário tem acesso ao estabelecimento
            $hasAccess = $user->establishments()
                ->where('establishment_id', $establishmentId)
                ->exists();
                
            if ($hasAccess) {
                return $next($request);
            }
        }
        
        // Se chegou aqui, não tem permissão
        abort(403, 'Acesso não autorizado.');
    }
}
