<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPasswordResetAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Se o usuário estiver autenticado, verifique se ele tem permissão para redefinir a senha
        if (Auth::check()) {
            $user = Auth::user();
            
            // Apenas super_admins e admins podem redefinir senhas
            if (!$user->isSuperAdmin() && !$user->isAdmin()) {
                return redirect()->route('login')
                    ->with('status', 'Você não tem permissão para acessar esta página.');
            }
        }

        return $next($request);
    }
}
