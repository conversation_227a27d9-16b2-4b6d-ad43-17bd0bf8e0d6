<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckEstablishmentAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $establishment = $request->route('establishment');

        // Se não estiver autenticado, redireciona para o login
        if (!$user) {
            return redirect()->route('login');
        }

        // Se for super admin, permite o acesso
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Verifica se o estabelecimento existe
        if (!$establishment) {
            abort(404, 'Estabelecimento não encontrado.');
        }

        // Verifica se o usuário tem acesso ao estabelecimento
        $hasAccess = $user->establishments()
            ->where('establishments.id', $establishment->id)
            ->exists();

        if (!$hasAccess) {
            abort(403, 'Você não tem permissão para acessar este estabelecimento.');
        }

        return $next($request);
    }
}
