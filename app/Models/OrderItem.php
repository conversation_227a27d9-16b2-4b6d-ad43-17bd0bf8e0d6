<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'dish_id',
        'service_id',
        'quantity',
        'unit_price',
        'customizations',
        'notes',
    ];

    protected $casts = [
        'customizations' => 'array',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function dish()
    {
        return $this->belongsTo(Dish::class);
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }
}
