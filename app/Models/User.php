<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function establishments()
    {
        return $this->belongsToMany(Establishment::class, 'establishment_user')
                    ->withPivot('role')
                    ->withTimestamps();
    }

    public function hasAccessToEstablishment($establishmentId)
    {
        return $this->establishments()->where('establishment_id', $establishmentId)->exists();
    }

    public function getRoleInEstablishment($establishmentId)
    {
        $establishment = $this->establishments()->where('establishment_id', $establishmentId)->first();
        return $establishment ? $establishment->pivot->role : null;
    }

    public function isSuperAdmin()
    {
        return $this->role === 'super_admin';
    }

    public function isAdmin()
    {
        return $this->role === 'admin' || $this->isSuperAdmin();
    }
}
