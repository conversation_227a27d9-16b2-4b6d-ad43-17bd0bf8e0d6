<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Location extends Model
{
    use HasFactory;

    protected $fillable = [
        'establishment_id',
        'identifier',
        'name',
        'type',
        'capacity',
        'status',
        'qr_code',
        'customer_name',
        'occupied_at',
        'available_at',
        'floor',
        'section',
    ];

    protected $casts = [
        'occupied_at' => 'datetime',
        'available_at' => 'datetime',
    ];

    public function establishment(): BelongsTo
    {
        return $this->belongsTo(Establishment::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function generateQRCode(): string
    {
        $type = Str::slug($this->type);
        $base = Str::slug($this->identifier, '-');
        $random = Str::lower(Str::random(8));
        $this->qr_code = "{$type}-{$base}-{$random}";
        $this->save();
        return $this->qr_code;
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->establishment->type === 'hotel'
            ? "Quarto {$this->identifier}"
            : "Mesa {$this->identifier}";
    }
}
