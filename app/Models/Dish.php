<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Dish extends Model
{
    use HasFactory;

    protected $fillable = [
        'establishment_id',
        'name',
        'description',
        'price',
        'category',
        'ingredients',
        'dietary_restrictions',
        'customizable_options',
        'image_url',
        'available',
    ];

    protected $casts = [
        'ingredients' => 'array',
        'dietary_restrictions' => 'array',
        'customizable_options' => 'array',
        'available' => 'boolean',
    ];

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function establishment()
    {
        return $this->belongsTo(Establishment::class);
    }
}
