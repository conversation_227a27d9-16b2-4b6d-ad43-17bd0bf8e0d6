<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Establishment extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'address',
        'phone',
        'email',
        'logo',
        'banner',
        'active',
        'type',
        'opening_hours',
        'delivery_available',
        'takeaway_available',
        'dine_in_available',
        'delivery_fee',
        'min_order_value',
        'estimated_delivery_time',
        'payment_methods',
        'delivery_radius',
        'cuisine_type',
        'average_rating',
        'total_ratings',
        'slug',
    ];

    protected $casts = [
        'active' => 'boolean',
        'delivery_available' => 'boolean',
        'takeaway_available' => 'boolean',
        'dine_in_available' => 'boolean',
        'delivery_fee' => 'decimal:2',
        'min_order_value' => 'decimal:2',
        'estimated_delivery_time' => 'integer',
        'delivery_radius' => 'integer',
        'average_rating' => 'decimal:1',
        'total_ratings' => 'integer',
        'opening_hours' => 'array',
        'payment_methods' => 'array',
    ];

    public function users()
    {
        return $this->belongsToMany(User::class, 'establishment_user')
                    ->withPivot('role')
                    ->withTimestamps();
    }

    public function locations()
    {
        return $this->hasMany(Location::class);
    }

    public function dishes()
    {
        return $this->hasMany(Dish::class);
    }

    public function services()
    {
        return $this->hasMany(Service::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    public function scopeWithUserAccess($query, $userId)
    {
        return $query->whereHas('users', function($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }

    public function getLocationTypeAttribute()
    {
        return $this->type === 'hotel' ? 'Quarto' : 'Mesa';
    }

    public function getCustomerTypeAttribute()
    {
        return $this->type === 'hotel' ? 'Hóspede' : 'Cliente';
    }
}
