<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PwaController;
use App\Http\Controllers\SuperAdminController;
use App\Http\Controllers\EstablishmentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// PWA Customer Routes
Route::get('/c/{establishment:slug}/{location:qr_code}', [PwaController::class, 'showMenu'])
    ->name('customer.pwa.menu');

Route::get('/c/{establishment:slug}/{location:qr_code}/{type}/{category}', [PwaController::class, 'showCategory'])
    ->name('customer.pwa.category')
    ->where(['type' => 'dish|service']);

Route::get('/c/{establishment:slug}/{location:qr_code}/cart', [PwaController::class, 'showCart'])
    ->name('customer.pwa.cart');

Route::get('/c/{establishment:slug}/{location:qr_code}/qr-scanner', [PwaController::class, 'showQRScanner'])
    ->name('customer.pwa.qr-scanner');

Route::get('/c/{establishment:slug}/{location:qr_code}/chat', [PwaController::class, 'showChat'])
    ->name('customer.pwa.chat');

Route::get('/c/{establishment:slug}/{location:qr_code}/item/{type}/{id}', [PwaController::class, 'showItemDetails'])
    ->name('customer.pwa.item-details')
    ->where(['type' => 'dish|service', 'id' => '[0-9]+']);

Route::get('/c/{establishment:slug}/{location:qr_code}/order-confirmation/{orderId?}', [PwaController::class, 'showOrderConfirmation'])
    ->name('customer.pwa.order-confirmation')
    ->where(['orderId' => '[0-9]+']);

Route::prefix('api/customer')->name('api.customer.')->group(function () {
    Route::post('/session/start', [PwaController::class, 'startSession'])->name('session.start');
    Route::post('/order/place', [PwaController::class, 'placeOrder'])->name('order.place');
});


// Public home route
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Auth routes
require __DIR__.'/auth.php';

// Authenticated user routes
Route::middleware('auth')->group(function () {
    // User profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Dashboard padrão (para usuários comuns, se necessário)
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Rotas do Super Admin
    Route::prefix('super-admin')
        ->name('super-admin.')
        ->middleware('superadmin')
        ->group(function () {
            // Dashboard
            Route::get('/dashboard', [SuperAdminController::class, 'dashboard'])->name('dashboard');

            // Gerenciamento de Usuários
            Route::prefix('users')->name('users.')->group(function () {
                Route::get('/', [SuperAdminController::class, 'users'])->name('index');
                Route::get('/create', [SuperAdminController::class, 'createUser'])->name('create');
                Route::post('/', [SuperAdminController::class, 'storeUser'])->name('store');
                Route::get('/{user}/edit', [SuperAdminController::class, 'editUser'])->name('edit');
                Route::put('/{user}', [SuperAdminController::class, 'updateUser'])->name('update');
                Route::delete('/{user}', [SuperAdminController::class, 'destroyUser'])->name('destroy');
            });

            // Gerenciamento de Estabelecimentos
            Route::prefix('establishments')->name('establishments.')->group(function () {
                Route::get('/', [SuperAdminController::class, 'establishments'])->name('index');
                Route::get('/create', [SuperAdminController::class, 'createEstablishment'])->name('create');
                Route::post('/', [SuperAdminController::class, 'storeEstablishment'])->name('store');
                Route::get('/{establishment}', [SuperAdminController::class, 'showEstablishment'])->name('show');
                Route::post('/{establishment}/toggle-status', [SuperAdminController::class, 'toggleEstablishmentStatus'])->name('toggle-status');
                Route::get('/{establishment}/edit', [SuperAdminController::class, 'editEstablishment'])->name('edit');
                Route::put('/{establishment}', [SuperAdminController::class, 'updateEstablishment'])->name('update');
                Route::delete('/{establishment}', [SuperAdminController::class, 'destroyEstablishment'])->name('destroy');
            });

            // Rotas de Análise
            Route::get('/analytics', [SuperAdminController::class, 'analytics'])->name('analytics');
        });

// Rotas para estabelecimentos (Admin)
Route::prefix('establishment/{establishmentId}')->name('establishment.')->middleware(['auth'])->group(function () {
    Route::get('/dashboard', [EstablishmentController::class, 'dashboard'])->name('dashboard');

    // Pedidos
    Route::get('/orders', [EstablishmentController::class, 'orders'])->name('orders.index');
    Route::patch('/orders/{order}/status', [EstablishmentController::class, 'updateOrderStatus'])->name('orders.update-status');

    // Locais (quartos/mesas)
    Route::get('/locations', [EstablishmentController::class, 'locations'])->name('locations.index');
    Route::get('/locations/create', [EstablishmentController::class, 'createLocation'])->name('locations.create');
    Route::post('/locations', [EstablishmentController::class, 'storeLocation'])->name('locations.store');
    Route::get('/locations/{location}/edit', [EstablishmentController::class, 'editLocation'])->name('locations.edit');
    Route::put('/locations/{location}', [EstablishmentController::class, 'updateLocation'])->name('locations.update');
    Route::delete('/locations/{location}', [EstablishmentController::class, 'destroyLocation'])->name('locations.destroy');
    Route::get('/locations/{locationId}/qrcode', [EstablishmentController::class, 'showQRCode'])->name('locations.qrcode');

    // Pratos
    Route::get('/dishes', [EstablishmentController::class, 'dishes'])->name('dishes.index');
    Route::get('/dishes/create', [EstablishmentController::class, 'createDish'])->name('dishes.create');
    Route::post('/dishes', [EstablishmentController::class, 'storeDish'])->name('dishes.store');
    Route::get('/dishes/{dish}/edit', [EstablishmentController::class, 'editDish'])->name('dishes.edit');
    Route::put('/dishes/{dish}', [EstablishmentController::class, 'updateDish'])->name('dishes.update');
    Route::delete('/dishes/{dish}', [EstablishmentController::class, 'destroyDish'])->name('dishes.destroy');

    // Serviços
    Route::get('/services', [EstablishmentController::class, 'services'])->name('services.index');
    Route::get('/services/create', [EstablishmentController::class, 'createService'])->name('services.create');
    Route::post('/services', [EstablishmentController::class, 'storeService'])->name('services.store');

    // Analytics
    Route::get('/analytics', [EstablishmentController::class, 'analytics'])->name('analytics');
});

    // Redirecionamento para o painel apropriado com base no tipo de usuário
    Route::get('/admin', function () {
        $user = request()->user();

        if ($user->isSuperAdmin()) {
            return redirect()->route('super-admin.dashboard');
        }

        if ($user->isAdmin()) {
            $establishment = $user->establishments->first();

            if ($establishment) {
                return redirect()->route('establishment.dashboard', $establishment);
            }
        }

        // Usuário comum
        return abort(403);
    })->name('admin');
});
