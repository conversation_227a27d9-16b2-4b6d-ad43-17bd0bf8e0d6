<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('establishment_id')->constrained()->onDelete('cascade');
            $table->string('identifier'); // Número do quarto ou mesa
            $table->string('name')->nullable(); // Nome personalizado
            $table->enum('type', ['room', 'table', 'counter', 'booth'])->default('table');
            $table->integer('capacity')->nullable();
            $table->enum('status', ['available', 'occupied', 'maintenance', 'reserved'])->default('available');
            $table->string('qr_code')->unique()->nullable();
            $table->string('customer_name')->nullable();
            $table->datetime('occupied_at')->nullable();
            $table->datetime('available_at')->nullable();
            $table->integer('floor')->nullable();
            $table->string('section')->nullable();
            $table->timestamps();

            $table->unique(['establishment_id', 'identifier']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
