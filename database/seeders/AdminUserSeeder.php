<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Verifica se já existe um super admin
        if (User::where('email', '<EMAIL>')->doesntExist()) {
            User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'), // Altere esta senha em produção!
                'remember_token' => Str::random(10),
                'role' => 'super_admin',
            ]);
            
            $this->command->info('Super admin criado com sucesso!');
            $this->command->warn('Email: <EMAIL>');
            $this->command->warn('Senha: password');
        } else {
            $this->command->info('Super admin já existe.');
        }
    }
}
