@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>Detalhes da Tarefa</h2>
            <div>
                <a href="{{ route('tasks.edit', $task) }}" class="btn btn-warning">Editar</a>
                <form action="{{ route('tasks.destroy', $task) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Tem certeza que deseja excluir esta tarefa?')">Excluir</button>
                </form>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <h5>Título</h5>
                <p class="lead">{{ $task->title }}</p>
            </div>
            
            <div class="mb-3">
                <h5>Descrição</h5>
                <p>{{ $task->description ?? 'Sem descrição' }}</p>
            </div>
            
            <div class="mb-3">
                <h5>Status</h5>
                @if($task->status == 'pendente')
                    <span class="badge bg-warning">Pendente</span>
                @elseif($task->status == 'em_andamento')
                    <span class="badge bg-primary">Em andamento</span>
                @else
                    <span class="badge bg-success">Concluída</span>
                @endif
            </div>
            
            <div class="mb-3">
                <h5>Criada em</h5>
                <p>{{ $task->created_at->format('d/m/Y H:i') }}</p>
            </div>
            
            <div class="mb-3">
                <h5>Atualizada em</h5>
                <p>{{ $task->updated_at->format('d/m/Y H:i') }}</p>
            </div>
            
            <div class="mt-4">
                <a href="{{ route('tasks.index') }}" class="btn btn-secondary">Voltar para a lista</a>
            </div>
        </div>
    </div>
@endsection
