@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Gerenciar Cardápio</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('establishment.dishes.create', $establishment->id) }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Novo Prato
            </a>
        </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="categoryFilter" class="form-label">Filtrar por Categoria</label>
                            <select class="form-select" id="categoryFilter">
                                <option value="">Todas as categorias</option>
                                @foreach($dishes->pluck('category')->unique() as $category)
                                    <option value="{{ $category }}">{{ ucfirst($category) }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="statusFilter" class="form-label">Filtrar por Status</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">Todos</option>
                                <option value="available">Disponível</option>
                                <option value="unavailable">Indisponível</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchFilter" class="form-label">Buscar</label>
                            <input type="text" class="form-control" id="searchFilter" placeholder="Nome do prato...">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="dishesContainer">
        @foreach($dishes as $dish)
            <div class="col-md-6 col-lg-4 mb-4 dish-card" 
                 data-category="{{ $dish->category }}" 
                 data-available="{{ $dish->available ? 'available' : 'unavailable' }}"
                 data-name="{{ strtolower($dish->name) }}">
                <div class="card h-100">
                    @if($dish->image_url)
                        <img src="{{ $dish->image_url }}" class="card-img-top" style="height: 200px; object-fit: cover;">
                    @else
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-utensils fa-3x text-muted"></i>
                        </div>
                    @endif
                    
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ $dish->name }}</h5>
                            <span class="badge {{ $dish->available ? 'bg-success' : 'bg-danger' }}">
                                {{ $dish->available ? 'Disponível' : 'Indisponível' }}
                            </span>
                        </div>
                        
                        <p class="card-text">{{ Str::limit($dish->description, 100) }}</p>
                        
                        <div class="mb-2">
                            <span class="badge bg-secondary">{{ ucfirst($dish->category) }}</span>
                            <span class="badge bg-primary">R$ {{ number_format($dish->price, 2, ',', '.') }}</span>
                        </div>
                        
                        @if($dish->dietary_restrictions)
                            <div class="mb-2">
                                @foreach($dish->dietary_restrictions as $restriction)
                                    <span class="badge bg-info">{{ $restriction }}</span>
                                @endforeach
                            </div>
                        @endif
                        
                        <div class="mb-3">
                            <small class="text-muted">
                                <strong>Ingredientes:</strong> {{ Str::limit(implode(', ', $dish->ingredients), 80) }}
                            </small>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ route('establishment.dishes.edit', [$establishment->id, $dish->id]) }}" 
                               class="btn btn-outline-warning">
                                <i class="fas fa-edit"></i> Editar
                            </a>
                            <button class="btn btn-outline-{{ $dish->available ? 'danger' : 'success' }}" 
                                    onclick="toggleAvailability({{ $dish->id }}, {{ $dish->available ? 'false' : 'true' }})">
                                <i class="fas fa-{{ $dish->available ? 'eye-slash' : 'eye' }}"></i>
                                {{ $dish->available ? 'Desativar' : 'Ativar' }}
                            </button>
                            <button class="btn btn-outline-danger" onclick="confirmDelete({{ $dish->id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    @if($dishes->isEmpty())
        <div class="text-center py-5">
            <i class="fas fa-utensils fa-4x text-muted mb-3"></i>
            <h4>Nenhum prato cadastrado</h4>
            <p class="text-muted">Comece criando o primeiro prato do seu cardápio!</p>
            <a href="{{ route('establishment.dishes.create', $establishment->id) }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Criar Primeiro Prato
            </a>
        </div>
    @endif

    <div class="d-flex justify-content-center mt-4">
        {{ $dishes->links() }}
    </div>
@endsection

@section('scripts')
<script>
    // Filtros
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
    document.getElementById('searchFilter').addEventListener('input', applyFilters);

    function applyFilters() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
        
        const dishes = document.querySelectorAll('.dish-card');
        
        dishes.forEach(dish => {
            const category = dish.dataset.category;
            const available = dish.dataset.available;
            const name = dish.dataset.name;
            
            let show = true;
            
            if (categoryFilter && category !== categoryFilter) show = false;
            if (statusFilter && available !== statusFilter) show = false;
            if (searchFilter && !name.includes(searchFilter)) show = false;
            
            dish.style.display = show ? 'block' : 'none';
        });
    }

    function toggleAvailability(dishId, newStatus) {
        fetch(`/establishment/{{ $establishment->id }}/dishes/${dishId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ available: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao alterar status do prato');
        });
    }

    function confirmDelete(dishId) {
        if (confirm('Tem certeza que deseja excluir este prato? Esta ação não pode ser desfeita.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/establishment/{{ $establishment->id }}/dishes/${dishId}`;
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '{{ csrf_token() }}';
            
            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection
