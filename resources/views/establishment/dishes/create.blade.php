@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Criar Novo Prato</h1>
        <a href="{{ route('establishment.dishes.index', $establishment->id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            {{-- Exibir erros gerais de validação --}}
            @if ($errors->any())
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Erro ao criar o prato:</h6>
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('establishment.dishes.store', $establishment->id) }}" method="POST" id="dishForm">
                @csrf

                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3">Informações Básicas</h5>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nome do Prato</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required
                                   placeholder="Ex: Hambúrguer Artesanal, Salmão Grelhado">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descrição</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="4" required
                                      placeholder="Descreva o prato, modo de preparo, acompanhamentos...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Preço (R$)</label>
                                    <input type="number" step="0.01" min="0" class="form-control @error('price') is-invalid @enderror"
                                           id="price" name="price" value="{{ old('price') }}" required>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Categoria</label>
                                    <input type="text" class="form-control @error('category') is-invalid @enderror"
                                           id="category" name="category" value="{{ old('category') }}" required
                                           placeholder="Ex: Pratos Principais, Sobremesas, Bebidas"
                                           list="categories">
                                    <datalist id="categories">
                                        <option value="Entradas">
                                        <option value="Pratos Principais">
                                        <option value="Sobremesas">
                                        <option value="Bebidas">
                                        <option value="Lanches">
                                        <option value="Saladas">
                                        <option value="Massas">
                                        <option value="Carnes">
                                        <option value="Peixes">
                                        <option value="Vegetariano">
                                        <option value="Vegano">
                                    </datalist>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image_url" class="form-label">URL da Imagem (Opcional)</label>
                            <input type="url" class="form-control @error('image_url') is-invalid @enderror"
                                   id="image_url" name="image_url" value="{{ old('image_url') }}"
                                   placeholder="https://exemplo.com/imagem.jpg">
                            @error('image_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Cole o link de uma imagem do prato</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h5 class="mb-3">Ingredientes e Restrições</h5>

                        <div class="mb-3">
                            <label for="ingredients_input" class="form-label">Ingredientes <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('ingredients') is-invalid @enderror" id="ingredients_input"
                                   placeholder="Digite um ingrediente e pressione Enter">
                            <div class="form-text">Pressione Enter para adicionar cada ingrediente</div>
                            <div id="ingredients_list" class="mt-2"></div>
                            <input type="hidden" name="ingredients" id="ingredients_hidden" value="">
                            @error('ingredients')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div id="ingredients_error" class="invalid-feedback" style="display: none;">
                                Pelo menos um ingrediente é obrigatório.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Restrições Alimentares</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Vegetariano" id="vegetarian">
                                        <label class="form-check-label" for="vegetarian">Vegetariano</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Vegano" id="vegan">
                                        <label class="form-check-label" for="vegan">Vegano</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Sem Glúten" id="gluten_free">
                                        <label class="form-check-label" for="gluten_free">Sem Glúten</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Sem Lactose" id="lactose_free">
                                        <label class="form-check-label" for="lactose_free">Sem Lactose</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Sem Açúcar" id="sugar_free">
                                        <label class="form-check-label" for="sugar_free">Sem Açúcar</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Orgânico" id="organic">
                                        <label class="form-check-label" for="organic">Orgânico</label>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="dietary_restrictions" id="dietary_restrictions_hidden">
                        </div>

                        <div class="mb-3">
                            <label for="customizations_input" class="form-label">Opções de Customização</label>
                            <input type="text" class="form-control" id="customizations_input"
                                   placeholder="Ex: Ponto da carne, molho extra...">
                            <div class="form-text">Opções que o cliente pode escolher</div>
                            <div id="customizations_list" class="mt-2"></div>
                            <input type="hidden" name="customizable_options" id="customizations_hidden">
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb"></i>
                            <strong>Dicas:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Use descrições atrativas e detalhadas</li>
                                <li>Inclua todos os ingredientes principais</li>
                                <li>Marque as restrições alimentares corretamente</li>
                                <li>Adicione uma imagem de qualidade se possível</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('establishment.dishes.index', $establishment->id) }}" class="btn btn-secondary">
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Criar Prato
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    let ingredients = [];
    let customizations = [];

    // Ingredientes
    document.getElementById('ingredients_input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const ingredient = this.value.trim();
            if (ingredient && !ingredients.includes(ingredient)) {
                ingredients.push(ingredient);
                updateIngredientsList();
                this.value = '';
            }
        }
    });

    function updateIngredientsList() {
        const list = document.getElementById('ingredients_list');
        list.innerHTML = ingredients.map((ingredient, index) =>
            `<span class="badge bg-primary me-1 mb-1">
                ${ingredient}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeIngredient(${index})"></button>
            </span>`
        ).join('');

        document.getElementById('ingredients_hidden').value = JSON.stringify(ingredients);
    }

    function removeIngredient(index) {
        ingredients.splice(index, 1);
        updateIngredientsList();
    }

    // Customizações
    document.getElementById('customizations_input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const customization = this.value.trim();
            if (customization && !customizations.includes(customization)) {
                customizations.push(customization);
                updateCustomizationsList();
                this.value = '';
            }
        }
    });

    function updateCustomizationsList() {
        const list = document.getElementById('customizations_list');
        list.innerHTML = customizations.map((customization, index) =>
            `<span class="badge bg-secondary me-1 mb-1">
                ${customization}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeCustomization(${index})"></button>
            </span>`
        ).join('');

        document.getElementById('customizations_hidden').value = JSON.stringify(customizations);
    }

    function removeCustomization(index) {
        customizations.splice(index, 1);
        updateCustomizationsList();
    }

    // Restrições alimentares
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateDietaryRestrictions);
    });

    function updateDietaryRestrictions() {
        const restrictions = [];
        document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            restrictions.push(checkbox.value);
        });
        document.getElementById('dietary_restrictions_hidden').value = JSON.stringify(restrictions);
    }

    // Inicializar campos hidden
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('ingredients_hidden').value = JSON.stringify(ingredients);
        document.getElementById('customizations_hidden').value = JSON.stringify(customizations);
        document.getElementById('dietary_restrictions_hidden').value = JSON.stringify([]);
    });

    // Validação do formulário
    document.getElementById('dishForm').addEventListener('submit', function(e) {
        // Limpar erros anteriores
        document.getElementById('ingredients_error').style.display = 'none';
        document.getElementById('ingredients_input').classList.remove('is-invalid');

        // Validar ingredientes
        if (ingredients.length === 0) {
            e.preventDefault();
            document.getElementById('ingredients_error').style.display = 'block';
            document.getElementById('ingredients_input').classList.add('is-invalid');
            document.getElementById('ingredients_input').focus();

            // Scroll para o campo de ingredientes
            document.getElementById('ingredients_input').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            return false;
        }

        // Atualizar campos hidden antes do submit
        document.getElementById('ingredients_hidden').value = JSON.stringify(ingredients);
        document.getElementById('customizations_hidden').value = JSON.stringify(customizations);

        // Atualizar restrições alimentares
        updateDietaryRestrictions();
    });
</script>
@endsection
