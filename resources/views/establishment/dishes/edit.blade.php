@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Editar Prato: {{ $dish->name }}</h1>
        <a href="{{ route('establishment.dishes.index', $establishment->id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            {{-- Exibir erros gerais de validação --}}
            @if ($errors->any())
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Erro ao salvar o prato:</h6>
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('establishment.dishes.update', [$establishment->id, $dish->id]) }}" method="POST" id="dishForm">
                @csrf
                @method('PUT')

                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-3">Informações Básicas</h5>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nome do Prato</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name', $dish->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descrição</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="4" required>{{ old('description', $dish->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Preço (R$)</label>
                                    <input type="number" step="0.01" min="0" class="form-control @error('price') is-invalid @enderror"
                                           id="price" name="price" value="{{ old('price', $dish->price) }}" required>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Categoria</label>
                                    <input type="text" class="form-control @error('category') is-invalid @enderror"
                                           id="category" name="category" value="{{ old('category', $dish->category) }}" required
                                           list="categories">
                                    <datalist id="categories">
                                        <option value="Entradas">
                                        <option value="Pratos Principais">
                                        <option value="Sobremesas">
                                        <option value="Bebidas">
                                        <option value="Lanches">
                                        <option value="Saladas">
                                        <option value="Massas">
                                        <option value="Carnes">
                                        <option value="Peixes">
                                        <option value="Vegetariano">
                                        <option value="Vegano">
                                    </datalist>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image_url" class="form-label">URL da Imagem (Opcional)</label>
                            <input type="url" class="form-control @error('image_url') is-invalid @enderror"
                                   id="image_url" name="image_url" value="{{ old('image_url', $dish->image_url) }}">
                            @error('image_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="available" name="available" value="1"
                                       {{ old('available', $dish->available) ? 'checked' : '' }}>
                                <label class="form-check-label" for="available">
                                    Prato disponível no cardápio
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h5 class="mb-3">Ingredientes e Restrições</h5>

                        <div class="mb-3">
                            <label for="ingredients_input" class="form-label">Ingredientes <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('ingredients') is-invalid @enderror" id="ingredients_input"
                                   placeholder="Digite um ingrediente e pressione Enter">
                            <div class="form-text">Pressione Enter para adicionar cada ingrediente</div>
                            <div id="ingredients_list" class="mt-2"></div>
                            <input type="hidden" name="ingredients" id="ingredients_hidden" value="">
                            @error('ingredients')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div id="ingredients_error" class="invalid-feedback" style="display: none;">
                                Pelo menos um ingrediente é obrigatório.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Restrições Alimentares</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Vegetariano" id="vegetarian">
                                        <label class="form-check-label" for="vegetarian">Vegetariano</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Vegano" id="vegan">
                                        <label class="form-check-label" for="vegan">Vegano</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Sem Glúten" id="gluten_free">
                                        <label class="form-check-label" for="gluten_free">Sem Glúten</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Sem Lactose" id="lactose_free">
                                        <label class="form-check-label" for="lactose_free">Sem Lactose</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Sem Açúcar" id="sugar_free">
                                        <label class="form-check-label" for="sugar_free">Sem Açúcar</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="Orgânico" id="organic">
                                        <label class="form-check-label" for="organic">Orgânico</label>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="dietary_restrictions" id="dietary_restrictions_hidden">
                        </div>

                        <div class="mb-3">
                            <label for="customizations_input" class="form-label">Opções de Customização</label>
                            <input type="text" class="form-control" id="customizations_input"
                                   placeholder="Digite um ingrediente e depois aperte Enter">
                            <div class="form-text">Opções que o cliente pode escolher</div>
                            <div id="customizations_list" class="mt-2"></div>
                            <input type="hidden" name="customizable_options" id="customizations_hidden">
                        </div>

                        @if($dish->image_url)
                            <div class="mb-3">
                                <label class="form-label">Imagem Atual</label>
                                <div>
                                    <img src="{{ $dish->image_url }}" alt="{{ $dish->name }}"
                                         class="img-thumbnail" style="max-width: 200px;">
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> Excluir Prato
                    </button>
                    <div>
                        <a href="{{ route('establishment.dishes.index', $establishment->id) }}" class="btn btn-secondary me-2">
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    let ingredients = @json($dish->ingredients ?? []);
    let customizations = @json($dish->customizable_options ?? []);
    let dietaryRestrictions = @json($dish->dietary_restrictions ?? []);

    // Inicializar dados existentes
    document.addEventListener('DOMContentLoaded', function() {
        // Garantir que os arrays estejam inicializados
        if (!Array.isArray(ingredients)) ingredients = [];
        if (!Array.isArray(customizations)) customizations = [];
        if (!Array.isArray(dietaryRestrictions)) dietaryRestrictions = [];

        updateIngredientsList();
        updateCustomizationsList();

        // Marcar restrições alimentares existentes
        dietaryRestrictions.forEach(restriction => {
            const checkbox = document.querySelector(`input[value="${restriction}"]`);
            if (checkbox) checkbox.checked = true;
        });
        updateDietaryRestrictions();

        // Inicializar campos hidden
        document.getElementById('ingredients_hidden').value = JSON.stringify(ingredients);
        document.getElementById('customizations_hidden').value = JSON.stringify(customizations);
        document.getElementById('dietary_restrictions_hidden').value = JSON.stringify(dietaryRestrictions);
    });

    // Ingredientes
    document.getElementById('ingredients_input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const ingredient = this.value.trim();
            if (ingredient && !ingredients.includes(ingredient)) {
                ingredients.push(ingredient);
                updateIngredientsList();
                this.value = '';
            }
        }
    });

    function updateIngredientsList() {
        const list = document.getElementById('ingredients_list');
        list.innerHTML = ingredients.map((ingredient, index) =>
            `<span class="badge bg-primary me-1 mb-1">
                ${ingredient}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeIngredient(${index})"></button>
            </span>`
        ).join('');

        document.getElementById('ingredients_hidden').value = JSON.stringify(ingredients);
    }

    function removeIngredient(index) {
        ingredients.splice(index, 1);
        updateIngredientsList();
    }

    // Customizações
    document.getElementById('customizations_input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const customization = this.value.trim();
            if (customization && !customizations.includes(customization)) {
                customizations.push(customization);
                updateCustomizationsList();
                this.value = '';
            }
        }
    });

    function updateCustomizationsList() {
        const list = document.getElementById('customizations_list');
        list.innerHTML = customizations.map((customization, index) =>
            `<span class="badge bg-secondary me-1 mb-1">
                ${customization}
                <button type="button" class="btn-close btn-close-white ms-1" onclick="removeCustomization(${index})"></button>
            </span>`
        ).join('');

        document.getElementById('customizations_hidden').value = JSON.stringify(customizations);
    }

    function removeCustomization(index) {
        customizations.splice(index, 1);
        updateCustomizationsList();
    }

    // Restrições alimentares
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        if (checkbox.id !== 'available') {
            checkbox.addEventListener('change', updateDietaryRestrictions);
        }
    });

    function updateDietaryRestrictions() {
        const restrictions = [];
        document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
            if (checkbox.id !== 'available') {
                restrictions.push(checkbox.value);
            }
        });
        document.getElementById('dietary_restrictions_hidden').value = JSON.stringify(restrictions);
    }

    // Validação do formulário
    document.getElementById('dishForm').addEventListener('submit', function(e) {
        // Limpar erros anteriores
        document.getElementById('ingredients_error').style.display = 'none';
        document.getElementById('ingredients_input').classList.remove('is-invalid');

        // Validar ingredientes
        if (ingredients.length === 0) {
            e.preventDefault();
            document.getElementById('ingredients_error').style.display = 'block';
            document.getElementById('ingredients_input').classList.add('is-invalid');
            document.getElementById('ingredients_input').focus();

            // Scroll para o campo de ingredientes
            document.getElementById('ingredients_input').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            return false;
        }

        // Atualizar campos hidden antes do submit
        document.getElementById('ingredients_hidden').value = JSON.stringify(ingredients);
        document.getElementById('customizations_hidden').value = JSON.stringify(customizations);

        // Atualizar restrições alimentares
        updateDietaryRestrictions();
    });

    function confirmDelete() {
        if (confirm('Tem certeza que deseja excluir este prato? Esta ação não pode ser desfeita.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("establishment.dishes.destroy", [$establishment->id, $dish->id]) }}';

            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';

            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '{{ csrf_token() }}';

            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection
