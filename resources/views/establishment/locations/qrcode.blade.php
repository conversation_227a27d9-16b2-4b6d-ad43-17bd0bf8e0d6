@php use App\Helpers\LocationHelper; @endphp
    <!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code - {{ $location->display_name }} | {{ $establishment->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }

            .print-only {
                display: block !important;
            }
        }

        .print-only {
            display: none;
        }


        .qr-container {
            text-align: center;
            padding: 2rem;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header text-center">
                    <h3>{{ $establishment->name }}</h3>
                    <h5 class="text-muted">{{ $location->display_name }}</h5>
                </div>
                <div class="card-body">
                    <div class="qr-container">
                        <!-- Aqui você integraria uma biblioteca de QR Code como QR Code Generator -->
                        <div class="d-flex justify-content-center">
                            <div id="qrcode" class="mb-3 w-[256px]"></div>
                        </div>

                        <h4>{{ $location->display_name }}</h4>
                        <p class="text-muted">Escaneie para acessar o menu</p>
                        <small class="text-muted">Código: {{ $location->qr_code }}</small>
                    </div>

                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Informações
                                    d{{ LocationHelper::getArticle($location->type) }} {{ $establishment->location_type }}</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Identificador:</strong> {{ $location->identifier }}</li>
                                    @if($location->name)
                                        <li><strong>Nome:</strong> {{ $location->name }}</li>
                                    @endif
                                    @if($location->capacity)
                                        <li><strong>Capacidade:</strong> {{ $location->capacity }} pessoas</li>
                                    @endif
                                    @if($location->floor)
                                        <li><strong>Andar:</strong> {{ $location->floor }}</li>
                                    @endif
                                    @if($location->section)
                                        <li><strong>Seção:</strong> {{ $location->section }}</li>
                                    @endif
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Como usar</h6>
                                <ol class="small">
                                    <li>Abra a câmera do seu celular</li>
                                    <li>Aponte para o QR Code</li>
                                    <li>Toque no link que aparecer</li>
                                    <li>Faça seu pedido online!</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center no-print">
                    <button class="btn btn-primary me-2" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimir
                    </button>
                    <button class="btn btn-success me-2" onclick="downloadQR()">
                        <i class="fas fa-download"></i> Baixar
                    </button>
                    <a href="{{ route('establishment.locations.index', $establishment->id) }}"
                       class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/davidshimjs-qrcodejs@0.0.2/qrcode.min.js
"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Gerar QR Code com a rota correta
        const qrUrl = "{{ route('customer.pwa.menu', ['establishment' => $establishment->slug, 'location' => $location->qr_code]) }}";

        const qrContainer = document.getElementById('qrcode');

        if (qrContainer) {
            new QRCode(qrContainer, {
                text: qrUrl,
                width: 256,
                height: 256,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
        }
    });

    function downloadQR() {
        const canvas = document.querySelector('#qrcode canvas');
        const link = document.createElement('a');
        link.download = `qr-{{ $location->identifier }}-{{ $establishment->name }}.png`;
        link.href = canvas.toDataURL();
        link.click();
    }
</script>
</body>
</html>
