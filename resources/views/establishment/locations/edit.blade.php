@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Editar {{ $establishment->location_type }} {{ $location->identifier }}</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('establishment.locations.qrcode', [$establishment->id, $location->id]) }}" 
               class="btn btn-info me-2" target="_blank">
                <i class="fas fa-qrcode"></i> Ver QR Code
            </a>
            <a href="{{ route('establishment.locations.index', $establishment->id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('establishment.locations.update', [$establishment->id, $location->id]) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="identifier" class="form-label">
                                Identificador do {{ $establishment->location_type }}
                            </label>
                            <input type="text" class="form-control @error('identifier') is-invalid @enderror" 
                                   id="identifier" name="identifier" value="{{ old('identifier', $location->identifier) }}" required>
                            @error('identifier')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nome (Opcional)</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $location->name) }}">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="type" class="form-label">Tipo</label>
                            <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                @if($establishment->type === 'hotel')
                                    <option value="room" {{ old('type', $location->type) == 'room' ? 'selected' : '' }}>Quarto</option>
                                @else
                                    <option value="table" {{ old('type', $location->type) == 'table' ? 'selected' : '' }}>Mesa</option>
                                    <option value="counter" {{ old('type', $location->type) == 'counter' ? 'selected' : '' }}>Balcão</option>
                                    <option value="booth" {{ old('type', $location->type) == 'booth' ? 'selected' : '' }}>Cabine</option>
                                @endif
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="capacity" class="form-label">Capacidade</label>
                            <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                   id="capacity" name="capacity" value="{{ old('capacity', $location->capacity) }}" min="1" max="20">
                            @error('capacity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status" required>
                                <option value="available" {{ old('status', $location->status) == 'available' ? 'selected' : '' }}>Disponível</option>
                                <option value="occupied" {{ old('status', $location->status) == 'occupied' ? 'selected' : '' }}>Ocupado</option>
                                <option value="maintenance" {{ old('status', $location->status) == 'maintenance' ? 'selected' : '' }}>Manutenção</option>
                                <option value="reserved" {{ old('status', $location->status) == 'reserved' ? 'selected' : '' }}>Reservado</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        @if($establishment->type === 'hotel')
                            <div class="mb-3">
                                <label for="floor" class="form-label">Andar</label>
                                <input type="number" class="form-control @error('floor') is-invalid @enderror" 
                                       id="floor" name="floor" value="{{ old('floor', $location->floor) }}" min="0" max="50">
                                @error('floor')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="section" class="form-label">
                                {{ $establishment->type === 'hotel' ? 'Ala/Seção' : 'Área/Seção' }}
                            </label>
                            <input type="text" class="form-control @error('section') is-invalid @enderror" 
                                   id="section" name="section" value="{{ old('section', $location->section) }}">
                            @error('section')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3" id="customer-section" style="{{ $location->status === 'occupied' ? '' : 'display: none;' }}">
                            <label for="customer_name" class="form-label">
                                Nome {{ $establishment->customer_type }}
                            </label>
                            <input type="text" class="form-control @error('customer_name') is-invalid @enderror" 
                                   id="customer_name" name="customer_name" value="{{ old('customer_name', $location->customer_name) }}">
                            @error('customer_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Informações do QR Code</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-2"><strong>Código:</strong> {{ $location->qr_code }}</p>
                                <p class="mb-2"><strong>Criado em:</strong> {{ $location->created_at->format('d/m/Y H:i') }}</p>
                                <a href="{{ route('establishment.locations.qrcode', [$establishment->id, $location->id]) }}" 
                                   class="btn btn-sm btn-outline-primary" target="_blank">
                                    <i class="fas fa-qrcode"></i> Visualizar QR Code
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> Excluir {{ $establishment->location_type }}
                    </button>
                    <div>
                        <a href="{{ route('establishment.locations.index', $establishment->id) }}" class="btn btn-secondary me-2">
                            Cancelar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    document.getElementById('status').addEventListener('change', function() {
        const customerSection = document.getElementById('customer-section');
        if (this.value === 'occupied') {
            customerSection.style.display = 'block';
        } else {
            customerSection.style.display = 'none';
            document.getElementById('customer_name').value = '';
        }
    });

    function confirmDelete() {
        if (confirm('Tem certeza que deseja excluir este {{ strtolower($establishment->location_type) }}? Esta ação não pode ser desfeita.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("establishment.locations.destroy", [$establishment->id, $location->id]) }}';
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '{{ csrf_token() }}';
            
            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection
