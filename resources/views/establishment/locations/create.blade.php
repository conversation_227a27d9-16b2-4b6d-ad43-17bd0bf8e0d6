@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Criar {{ \App\Helpers\LocationHelper::getNewAdjective($establishment->type === 'hotel' ? 'room' : 'table') }} {{ $establishment->location_type }}</h1>
        <a href="{{ route('establishment.locations.index', $establishment->id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('establishment.locations.store', $establishment->id) }}" method="POST">
                @csrf

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="identifier" class="form-label">
                                Identificador do {{ $establishment->location_type }}
                            </label>
                            <input type="text" class="form-control @error('identifier') is-invalid @enderror"
                                   id="identifier" name="identifier" value="{{ old('identifier') }}" required
                                   placeholder="{{ $establishment->type === 'hotel' ? 'Ex: 101, 205A' : 'Ex: 1, 15, VIP-01' }}">
                            @error('identifier')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                Identificador único para o {{ strtolower($establishment->location_type) }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nome (Opcional)</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}"
                                   placeholder="{{ $establishment->type === 'hotel' ? 'Ex: Suíte Presidencial' : 'Ex: Mesa da Varanda' }}">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="type" class="form-label">Tipo</label>
                            <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                <option value="">Selecione o tipo</option>
                                @if($establishment->type === 'hotel')
                                    <option value="room" {{ old('type') == 'room' ? 'selected' : '' }}>Quarto</option>
                                @else
                                    <option value="table" {{ old('type') == 'table' ? 'selected' : '' }}>Mesa</option>
                                    <option value="counter" {{ old('type') == 'counter' ? 'selected' : '' }}>Balcão</option>
                                    <option value="booth" {{ old('type') == 'booth' ? 'selected' : '' }}>Cabine</option>
                                @endif
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="capacity" class="form-label">Capacidade</label>
                            <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                   id="capacity" name="capacity" value="{{ old('capacity') }}" min="1" max="20"
                                   placeholder="{{ $establishment->type === 'hotel' ? 'Número de hóspedes' : 'Número de pessoas' }}">
                            @error('capacity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        @if($establishment->type === 'hotel')
                            <div class="mb-3">
                                <label for="floor" class="form-label">Andar</label>
                                <input type="number" class="form-control @error('floor') is-invalid @enderror"
                                       id="floor" name="floor" value="{{ old('floor') }}" min="0" max="50">
                                @error('floor')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="section" class="form-label">
                                {{ $establishment->type === 'hotel' ? 'Ala/Seção' : 'Área/Seção' }}
                            </label>
                            <input type="text" class="form-control @error('section') is-invalid @enderror"
                                   id="section" name="section" value="{{ old('section') }}"
                                   placeholder="{{ $establishment->type === 'hotel' ? 'Ex: Ala Norte, Seção A' : 'Ex: Área Externa, Salão Principal' }}">
                            @error('section')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>QR Code:</strong> Será gerado automaticamente após a criação do {{ strtolower($establishment->location_type) }}.
                        </div>

                        @if($establishment->type === 'hotel')
                            <div class="alert alert-warning">
                                <i class="fas fa-bed"></i>
                                <strong>Dica:</strong> Use identificadores claros como "101" (andar 1, quarto 01) ou "205A" (andar 2, quarto 05, suíte A).
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-utensils"></i>
                                <strong>Dica:</strong> Numere as mesas de forma sequencial ou por área (Ex: 1-10 área interna, 11-20 área externa).
                            </div>
                        @endif
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('establishment.locations.index', $establishment->id) }}" class="btn btn-secondary">
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Criar {{ $establishment->location_type }}
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
