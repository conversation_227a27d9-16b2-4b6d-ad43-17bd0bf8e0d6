@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Gerenciar {{ $establishment->location_type }}s</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('establishment.locations.create', $establishment->id) }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {{ \App\Helpers\LocationHelper::getNewAdjective($establishment->type === 'hotel' ? 'room' : 'table') }} {{ $establishment->location_type }}
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Identificador</th>
                            <th>Nome</th>
                            <th>Tipo</th>
                            <th>Capacidade</th>
                            <th>Status</th>
                            <th>{{ $establishment->customer_type }} Atual</th>
                            <th>QR Code</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($locations as $location)
                            <tr>
                                <td><strong>{{ $location->identifier }}</strong></td>
                                <td>{{ $location->name ?: '-' }}</td>
                                <td>
                                    @if($location->type === 'room')
                                        <i class="fas fa-bed me-1"></i>Quarto
                                    @elseif($location->type === 'table')
                                        <i class="fas fa-chair me-1"></i>Mesa
                                    @elseif($location->type === 'counter')
                                        <i class="fas fa-desktop me-1"></i>Balcão
                                    @else
                                        <i class="fas fa-couch me-1"></i>Cabine
                                    @endif
                                </td>
                                <td>{{ $location->capacity ?: '-' }}</td>
                                <td>
                                    @if($location->status === 'available')
                                        <span class="badge bg-success">Disponível</span>
                                    @elseif($location->status === 'occupied')
                                        <span class="badge bg-warning">Ocupado</span>
                                    @elseif($location->status === 'maintenance')
                                        <span class="badge bg-danger">Manutenção</span>
                                    @else
                                        <span class="badge bg-info">Reservado</span>
                                    @endif
                                </td>
                                <td>{{ $location->customer_name ?: '-' }}</td>
                                <td>
                                    <a href="{{ route('establishment.locations.qrcode', [$establishment->id, $location->id]) }}"
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-qrcode"></i> Ver QR
                                    </a>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('establishment.locations.edit', [$establishment->id, $location->id]) }}"
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger"
                                                onclick="confirmDelete({{ $location->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {{ $locations->links() }}
        </div>
    </div>
@endsection

@section('scripts')
<script>
    function confirmDelete(locationId) {
        if (confirm('Tem certeza que deseja excluir est{{ \App\Helpers\LocationHelper::getArticle($establishment->type === 'hotel' ? 'room' : 'table') }} {{ strtolower($establishment->location_type) }}? Esta ação não pode ser desfeita.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/establishment/{{ $establishment->id }}/locations/${locationId}`;

            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';

            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '{{ csrf_token() }}';

            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection
