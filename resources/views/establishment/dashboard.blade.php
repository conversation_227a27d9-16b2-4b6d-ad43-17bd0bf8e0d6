@extends('layouts.establishment')

@section('content')
    <div class="establishment-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="mb-0">{{ $establishment->name }}</h2>
                <p class="mb-0 opacity-75">Painel de Controle - {{ ucfirst($establishment->type) }}</p>
            </div>
            <div class="text-end">
                <small>Última atualização: {{ now()->format('d/m/Y H:i') }}</small>
            </div>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pedidos Pendentes
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">{{ $pendingOrders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {{ $establishment->location_type }}s Ocupados
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">{{ $occupiedLocations }}/{{ $totalLocations }}</div>
                        </div>
                        <div class="col-auto">
                            @if($establishment->type === 'hotel')
                                <i class="fas fa-bed fa-2x text-gray-300"></i>
                            @else
                                <i class="fas fa-chair fa-2x text-gray-300"></i>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Receita Hoje
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                R$ {{ number_format($todayRevenue, 2, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Pedidos Hoje
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">{{ $todayOrders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pedidos Recentes -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Pedidos Recentes</h6>
                    <a href="{{ route('establishment.orders.index', $establishment->id) }}" class="btn btn-sm btn-primary">
                        Ver Todos
                    </a>
                </div>
                <div class="card-body">
                    @if($recentOrders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>{{ $establishment->location_type }}</th>
                                        <th>{{ $establishment->customer_type }}</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Horário</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentOrders as $order)
                                        <tr>
                                            <td>#{{ $order->id }}</td>
                                            <td>{{ $order->location->display_name }}</td>
                                            <td>{{ $order->customer_name }}</td>
                                            <td>
                                                @if($order->status === 'pending')
                                                    <span class="badge bg-warning">Pendente</span>
                                                @elseif($order->status === 'preparing')
                                                    <span class="badge bg-info">Preparando</span>
                                                @elseif($order->status === 'ready')
                                                    <span class="badge bg-primary">Pronto</span>
                                                @elseif($order->status === 'delivered')
                                                    <span class="badge bg-success">Entregue</span>
                                                @else
                                                    <span class="badge bg-danger">Cancelado</span>
                                                @endif
                                            </td>
                                            <td>R$ {{ number_format($order->total_amount, 2, ',', '.') }}</td>
                                            <td>{{ $order->created_at->format('H:i') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Nenhum pedido recente</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Pratos Mais Pedidos -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Pratos Mais Pedidos</h6>
                </div>
                <div class="card-body">
                    @if($topDishes->count() > 0)
                        @foreach($topDishes as $dish)
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <i class="fas fa-utensils fa-2x text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $dish->name }}</h6>
                                    <small class="text-muted">{{ $dish->total_quantity }} pedidos</small>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Nenhum dado disponível</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Ações Rápidas</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('establishment.orders.index', $establishment->id) }}" class="btn btn-warning w-100">
                                <i class="fas fa-list-alt mb-2"></i><br>
                                Gerenciar Pedidos
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('establishment.dishes.create', $establishment->id) }}" class="btn btn-success w-100">
                                <i class="fas fa-plus mb-2"></i><br>
                                Novo Prato
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('establishment.locations.create', $establishment->id) }}" class="btn btn-info w-100">
                                <i class="fas fa-door-open mb-2"></i><br>
                                Novo {{ $establishment->location_type }}
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('establishment.analytics', $establishment->id) }}" class="btn btn-primary w-100">
                                <i class="fas fa-chart-line mb-2"></i><br>
                                Ver Relatórios
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
