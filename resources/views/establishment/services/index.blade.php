@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Gerenciar Serviços</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('establishment.services.create', $establishment->id) }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Novo Serviço
            </a>
        </div>
    </div>

    <div class="row">
        @foreach($services as $service)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ $service->name }}</h5>
                            <span class="badge {{ $service->available ? 'bg-success' : 'bg-danger' }}">
                                {{ $service->available ? 'Disponível' : 'Indisponível' }}
                            </span>
                        </div>
                        
                        <p class="card-text">{{ $service->description }}</p>
                        
                        <div class="mb-2">
                            <span class="badge bg-secondary">{{ ucfirst($service->category) }}</span>
                            @if($service->price)
                                <span class="badge bg-primary">R$ {{ number_format($service->price, 2, ',', '.') }}</span>
                            @else
                                <span class="badge bg-info">Gratuito</span>
                            @endif
                        </div>
                        
                        @if($service->duration)
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> Duração: {{ $service->duration }} minutos
                                </small>
                            </div>
                        @endif
                    </div>
                    
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-outline-warning">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                            <button class="btn btn-outline-{{ $service->available ? 'danger' : 'success' }}">
                                <i class="fas fa-{{ $service->available ? 'eye-slash' : 'eye' }}"></i>
                                {{ $service->available ? 'Desativar' : 'Ativar' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    @if($services->isEmpty())
        <div class="text-center py-5">
            <i class="fas fa-concierge-bell fa-4x text-muted mb-3"></i>
            <h4>Nenhum serviço cadastrado</h4>
            <p class="text-muted">Comece criando o primeiro serviço do seu estabelecimento!</p>
            <a href="{{ route('establishment.services.create', $establishment->id) }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Criar Primeiro Serviço
            </a>
        </div>
    @endif

    <div class="d-flex justify-content-center mt-4">
        {{ $services->links() }}
    </div>
@endsection
