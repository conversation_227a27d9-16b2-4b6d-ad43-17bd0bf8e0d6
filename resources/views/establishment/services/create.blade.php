@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Criar Novo Serviço</h1>
        <a href="{{ route('establishment.services.index', $establishment->id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('establishment.services.store', $establishment->id) }}" method="POST">
                @csrf
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Nome do Serviço</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name') }}" required
                                   placeholder="Ex: Limpeza de Quarto, Room Service">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descrição</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required
                                      placeholder="Descreva o serviço oferecido...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="category" class="form-label">Categoria</label>
                            <input type="text" class="form-control @error('category') is-invalid @enderror" 
                                   id="category" name="category" value="{{ old('category') }}" required
                                   placeholder="Ex: Limpeza, Alimentação, Entretenimento"
                                   list="service_categories">
                            <datalist id="service_categories">
                                <option value="Limpeza">
                                <option value="Alimentação">
                                <option value="Entretenimento">
                                <option value="Transporte">
                                <option value="Lavanderia">
                                <option value="Spa & Bem-estar">
                                <option value="Concierge">
                                <option value="Manutenção">
                                <option value="Segurança">
                                <option value="Outros">
                            </datalist>
                            @error('category')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="price" class="form-label">Preço (R$) - Opcional</label>
                            <input type="number" step="0.01" min="0" class="form-control @error('price') is-invalid @enderror" 
                                   id="price" name="price" value="{{ old('price') }}"
                                   placeholder="Deixe vazio se for gratuito">
                            @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Deixe vazio para serviços gratuitos</div>
                        </div>

                        <div class="mb-3">
                            <label for="duration" class="form-label">Duração (minutos) - Opcional</label>
                            <input type="number" min="1" max="480" class="form-control @error('duration') is-invalid @enderror" 
                                   id="duration" name="duration" value="{{ old('duration') }}"
                                   placeholder="Ex: 30, 60, 120">
                            @error('duration')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Tempo estimado para execução do serviço</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Dica:</strong> Serviços bem descritos e categorizados facilitam a busca pelos clientes.
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('establishment.services.index', $establishment->id) }}" class="btn btn-secondary">
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Criar Serviço
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
