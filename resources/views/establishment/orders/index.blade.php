@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Gerenciar Pedidos</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> Atualizar
            </button>
        </div>
    </div>

    <!-- Filtros de Status -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="statusFilter" id="all" autocomplete="off" checked>
                        <label class="btn btn-outline-primary" for="all">Todos</label>

                        <input type="radio" class="btn-check" name="statusFilter" id="pending" autocomplete="off">
                        <label class="btn btn-outline-warning" for="pending">Pendentes</label>

                        <input type="radio" class="btn-check" name="statusFilter" id="preparing" autocomplete="off">
                        <label class="btn btn-outline-info" for="preparing">Preparando</label>

                        <input type="radio" class="btn-check" name="statusFilter" id="ready" autocomplete="off">
                        <label class="btn btn-outline-primary" for="ready">Prontos</label>

                        <input type="radio" class="btn-check" name="statusFilter" id="delivered" autocomplete="off">
                        <label class="btn btn-outline-success" for="delivered">Entregues</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>{{ $establishment->location_type }}</th>
                            <th>{{ $establishment->customer_type }}</th>
                            <th>Itens</th>
                            <th>Status</th>
                            <th>Total</th>
                            <th>Criado em</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($orders as $order)
                            <tr data-status="{{ $order->status }}">
                                <td><strong>#{{ $order->id }}</strong></td>
                                <td>{{ $order->location->display_name }}</td>
                                <td>{{ $order->customer_name }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#items-{{ $order->id }}">
                                        {{ $order->items->count() }} itens
                                    </button>
                                    <div class="collapse mt-2" id="items-{{ $order->id }}">
                                        <div class="card card-body">
                                            @foreach($order->items as $item)
                                                <div class="d-flex justify-content-between">
                                                    <span>
                                                        {{ $item->dish ? $item->dish->name : $item->service->name }}
                                                        ({{ $item->quantity }}x)
                                                    </span>
                                                    <span>R$ {{ number_format($item->unit_price * $item->quantity, 2, ',', '.') }}</span>
                                                </div>
                                                @if($item->customizations)
                                                    <small class="text-muted">Customizações: {{ implode(', ', $item->customizations) }}</small>
                                                @endif
                                                @if($item->notes)
                                                    <small class="text-muted">Obs: {{ $item->notes }}</small>
                                                @endif
                                            @endforeach
                                            @if($order->special_requests)
                                                <hr>
                                                <strong>Solicitações Especiais:</strong>
                                                <p class="mb-0">{{ $order->special_requests }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" onchange="updateOrderStatus({{ $order->id }}, this.value)">
                                        <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>Pendente</option>
                                        <option value="preparing" {{ $order->status == 'preparing' ? 'selected' : '' }}>Preparando</option>
                                        <option value="ready" {{ $order->status == 'ready' ? 'selected' : '' }}>Pronto</option>
                                        <option value="delivered" {{ $order->status == 'delivered' ? 'selected' : '' }}>Entregue</option>
                                        <option value="cancelled" {{ $order->status == 'cancelled' ? 'selected' : '' }}>Cancelado</option>
                                    </select>
                                </td>
                                <td><strong>R$ {{ number_format($order->total_amount, 2, ',', '.') }}</strong></td>
                                <td>{{ $order->created_at->format('d/m/Y H:i') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-info" onclick="printOrder({{ $order->id }})">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" onclick="sendNotification({{ $order->id }})">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {{ $orders->links() }}
        </div>
    </div>

    <!-- Modal de Notificação -->
    <div class="modal fade" id="notificationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Enviar Notificação</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="notificationForm">
                        <div class="mb-3">
                            <label for="notification_message" class="form-label">Mensagem</label>
                            <textarea class="form-control" id="notification_message" name="message" rows="3" 
                                      placeholder="Digite a mensagem para o cliente..." required></textarea>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                A notificação será enviada para o cliente do pedido selecionado.
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="submitNotification()">
                        <i class="fas fa-paper-plane"></i> Enviar Notificação
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    let selectedOrderId = null;

    function updateOrderStatus(orderId, status) {
        fetch(`/establishment/{{ $establishment->id }}/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mostrar feedback visual
                const row = event.target.closest('tr');
                row.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    row.style.backgroundColor = '';
                }, 1000);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao atualizar status');
        });
    }

    function sendNotification(orderId) {
        selectedOrderId = orderId;
        document.getElementById('notification_message').value = '';
        new bootstrap.Modal(document.getElementById('notificationModal')).show();
    }

    function submitNotification() {
        const message = document.getElementById('notification_message').value;
        
        if (!message.trim()) {
            alert('Por favor, digite uma mensagem.');
            return;
        }

        fetch(`/establishment/{{ $establishment->id }}/orders/${selectedOrderId}/notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Notificação enviada com sucesso!');
                bootstrap.Modal.getInstance(document.getElementById('notificationModal')).hide();
            } else {
                alert('Erro ao enviar notificação');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao enviar notificação');
        });
    }

    // Filtros de status
    document.querySelectorAll('input[name="statusFilter"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const status = this.id;
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });

    function printOrder(orderId) {
        // Implementar impressão do pedido
        window.print();
    }
</script>
@endsection
