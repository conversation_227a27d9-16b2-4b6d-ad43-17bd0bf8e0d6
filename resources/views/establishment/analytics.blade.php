@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Analytics e Relatórios</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData()">
                    <i class="fas fa-download"></i> Exportar
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printReport()">
                    <i class="fas fa-print"></i> Imprimir
                </button>
            </div>
        </div>
    </div>

    <!-- Métricas Principais -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Receita Total
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                R$ {{ number_format($totalRevenue, 2, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total de Pedidos
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">{{ $totalOrders }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Ticket Médio
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                R$ {{ number_format($averageOrderValue, 2, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Receita Mensal
                            </div>
                            <div class="h4 mb-0 font-weight-bold text-gray-800">
                                R$ {{ number_format($monthlyRevenue, 2, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Gráfico de Receita por Dia -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Receita dos Últimos 30 Dias</h6>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Gráfico de Status dos Pedidos -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Status dos Pedidos</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Receita por Hora -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Receita por Hora (Últimos 7 dias)</h6>
                </div>
                <div class="card-body">
                    <canvas id="hourlyChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Pratos Mais Vendidos -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Top 10 Pratos Mais Vendidos</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Prato</th>
                                    <th>Qtd</th>
                                    <th>Receita</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topDishes as $dish)
                                    <tr>
                                        <td>{{ $dish->name }}</td>
                                        <td>{{ $dish->total_quantity }}</td>
                                        <td>R$ {{ number_format($dish->total_revenue, 2, ',', '.') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Uso de Locais -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">{{ $establishment->location_type }}s Mais Utilizados</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{{ $establishment->location_type }}</th>
                                    <th>Nome</th>
                                    <th>Total de Pedidos</th>
                                    <th>Receita Total</th>
                                    <th>Receita Média</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($locationUsage as $location)
                                    <tr>
                                        <td><strong>{{ $location->identifier }}</strong></td>
                                        <td>{{ $location->name ?: '-' }}</td>
                                        <td>{{ $location->orders_count }}</td>
                                        <td>R$ {{ number_format($location->total_revenue, 2, ',', '.') }}</td>
                                        <td>R$ {{ number_format($location->total_revenue / $location->orders_count, 2, ',', '.') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    // Gráfico de Receita por Dia
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    const revenueData = @json($ordersByDay);
    
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: revenueData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('pt-BR');
            }),
            datasets: [{
                label: 'Receita (R$)',
                data: revenueData.map(item => item.revenue),
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                yAxisID: 'y'
            }, {
                label: 'Pedidos',
                data: revenueData.map(item => item.count),
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.3,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Receita (R$)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Número de Pedidos'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });

    // Gráfico de Status dos Pedidos
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusData = @json($ordersByStatus);
    
    const statusLabels = {
        'pending': 'Pendentes',
        'preparing': 'Preparando',
        'ready': 'Prontos',
        'delivered': 'Entregues',
        'cancelled': 'Cancelados'
    };
    
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusData.map(item => statusLabels[item.status] || item.status),
            datasets: [{
                data: statusData.map(item => item.count),
                backgroundColor: [
                    '#f6c23e',
                    '#36b9cc',
                    '#4e73df',
                    '#1cc88a',
                    '#e74a3b'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Gráfico de Receita por Hora
    const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
    const hourlyData = @json($revenueByHour);
    
    // Criar array com todas as horas (0-23)
    const allHours = Array.from({length: 24}, (_, i) => i);
    const hourlyRevenue = allHours.map(hour => {
        const data = hourlyData.find(item => item.hour == hour);
        return data ? data.revenue : 0;
    });
    
    new Chart(hourlyCtx, {
        type: 'bar',
        data: {
            labels: allHours.map(hour => `${hour}:00`),
            datasets: [{
                label: 'Receita por Hora',
                data: hourlyRevenue,
                backgroundColor: '#4e73df',
                borderColor: '#4e73df',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Receita (R$)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Hora do Dia'
                    }
                }
            }
        }
    });

    function exportData() {
        // Implementar exportação de dados
        alert('Funcionalidade de exportação será implementada');
    }

    function printReport() {
        window.print();
    }
</script>
@endsection
