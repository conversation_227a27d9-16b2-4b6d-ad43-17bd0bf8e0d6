@extends('layouts.establishment')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Configurações do Estabelecimento</h1>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('establishment.settings.update', $establishment->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <!-- Informações Básicas -->
                    <div class="col-md-6">
                        <h5 class="mb-3">Informações Básicas</h5>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Nome do Estabelecimento</label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $establishment->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descrição</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $establishment->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Endereço</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="2">{{ old('address', $establishment->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Telefone</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $establishment->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">E-mail</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $establishment->email) }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="logo_url" class="form-label">URL do Logo</label>
                            <input type="url" class="form-control @error('logo_url') is-invalid @enderror" 
                                   id="logo_url" name="logo_url" value="{{ old('logo_url', $establishment->logo_url) }}">
                            @error('logo_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Configurações Avançadas -->
                    <div class="col-md-6">
                        <h5 class="mb-3">Configurações de Localização e Entrega</h5>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Geovalidação:</strong> Configure as coordenadas e o raio para validar se os clientes estão próximos ao estabelecimento antes de fazer pedidos.
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">Latitude</label>
                                    <input type="number" step="any" class="form-control @error('latitude') is-invalid @enderror" 
                                           id="latitude" name="latitude" value="{{ old('latitude', $establishment->settings['latitude'] ?? '') }}">
                                    @error('latitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">Longitude</label>
                                    <input type="number" step="any" class="form-control @error('longitude') is-invalid @enderror" 
                                           id="longitude" name="longitude" value="{{ old('longitude', $establishment->settings['longitude'] ?? '') }}">
                                    @error('longitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="getCurrentLocation()">
                                <i class="fas fa-map-marker-alt"></i> Obter Localização Atual
                            </button>
                        </div>

                        <div class="mb-3">
                            <label for="delivery_radius" class="form-label">Raio de Entrega (metros)</label>
                            <input type="number" class="form-control @error('delivery_radius') is-invalid @enderror" 
                                   id="delivery_radius" name="delivery_radius" min="10" max="5000"
                                   value="{{ old('delivery_radius', $establishment->settings['delivery_radius'] ?? 500) }}">
                            @error('delivery_radius')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Distância máxima que os clientes podem estar para fazer pedidos</div>
                        </div>

                        <div class="mb-3">
                            <label for="delivery_time" class="form-label">Tempo de Entrega Estimado (minutos)</label>
                            <input type="number" class="form-control @error('delivery_time') is-invalid @enderror" 
                                   id="delivery_time" name="delivery_time" min="5" max="120"
                                   value="{{ old('delivery_time', $establishment->settings['delivery_time'] ?? 30) }}">
                            @error('delivery_time')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <h5 class="mb-3 mt-4">Personalização</h5>

                        <div class="mb-3">
                            <label for="theme_color" class="form-label">Cor do Tema</label>
                            <input type="color" class="form-control form-control-color @error('theme_color') is-invalid @enderror" 
                                   id="theme_color" name="theme_color" 
                                   value="{{ old('theme_color', $establishment->settings['theme_color'] ?? '#007bff') }}">
                            @error('theme_color')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <h5 class="mb-3 mt-4">Configurações de Sistema</h5>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifications_enabled" 
                                       name="notifications_enabled" value="1"
                                       {{ old('notifications_enabled', $establishment->settings['notifications_enabled'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="notifications_enabled">
                                    Habilitar Notificações
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_accept_orders" 
                                       name="auto_accept_orders" value="1"
                                       {{ old('auto_accept_orders', $establishment->settings['auto_accept_orders'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="auto_accept_orders">
                                    Aceitar Pedidos Automaticamente
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('establishment.dashboard', $establishment->id) }}" class="btn btn-secondary">
                        Voltar ao Dashboard
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar Configurações
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    function getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                document.getElementById('latitude').value = position.coords.latitude;
                document.getElementById('longitude').value = position.coords.longitude;
                
                alert('Localização obtida com sucesso!');
            }, function(error) {
                alert('Erro ao obter localização: ' + error.message);
            });
        } else {
            alert('Geolocalização não é suportada neste navegador.');
        }
    }
</script>
@endsection
