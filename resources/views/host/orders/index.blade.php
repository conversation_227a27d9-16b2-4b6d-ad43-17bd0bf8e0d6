@extends('layouts.host')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Gerenciar Pedidos</h1>
        <div>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> Atualizar
            </button>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Quarto</th>
                    <th>Hóspede</th>
                    <th>Status</th>
                    <th>Total</th>
                    <th>Criado em</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                @foreach($orders as $order)
                    <tr>
                        <td>{{ $order->id }}</td>
                        <td>{{ $order->room->number }}</td>
                        <td>{{ $order->guest_name }}</td>
                        <td>
                            <select class="form-select form-select-sm" onchange="updateOrderStatus({{ $order->id }}, this.value)">
                                <option value="pendente" {{ $order->status == 'pendente' ? 'selected' : '' }}>Pendente</option>
                                <option value="preparando" {{ $order->status == 'preparando' ? 'selected' : '' }}>Preparando</option>
                                <option value="a_caminho" {{ $order->status == 'a_caminho' ? 'selected' : '' }}>A Caminho</option>
                                <option value="concluido" {{ $order->status == 'concluido' ? 'selected' : '' }}>Concluído</option>
                                <option value="cancelado" {{ $order->status == 'cancelado' ? 'selected' : '' }}>Cancelado</option>
                            </select>
                        </td>
                        <td>R$ {{ number_format($order->total_amount, 2, ',', '.') }}</td>
                        <td>{{ $order->created_at->format('d/m/Y H:i') }}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewOrderDetails({{ $order->id }})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    {{ $orders->links() }}
@endsection

@section('scripts')
<script>
    function updateOrderStatus(orderId, status) {
        fetch(`/host/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mostrar feedback visual
                const row = event.target.closest('tr');
                row.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    row.style.backgroundColor = '';
                }, 1000);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao atualizar status');
        });
    }
</script>
@endsection
