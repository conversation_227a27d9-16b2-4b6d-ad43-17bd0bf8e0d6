@extends('layouts.super-admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Editar Usuário: {{ $user->name }}</h1>
        <a href="{{ route('super-admin.users.index') }}" class="text-gray-600 hover:text-gray-800">
            Voltar para lista
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <form action="{{ route('super-admin.users.update', $user) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input type="text" name="name" id="name" value="{{ old('name', $user->name) }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                       required>
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">E-mail</label>
                <input type="email" name="email" id="email" value="{{ old('email', $user->email) }}"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                       required>
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Nova Senha</label>
                <input type="password" name="password" id="password"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                       placeholder="Deixe em branco para manter a senha atual">
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">Confirmar Nova Senha</label>
                <input type="password" name="password_confirmation" id="password_confirmation"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            </div>

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Tipo de Usuário</label>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <input id="role_admin" name="role" type="radio" value="admin" 
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                               {{ old('role', $user->role) === 'admin' ? 'checked' : '' }}>
                        <label for="role_admin" class="ml-2 block text-sm text-gray-700">
                            Administrador
                        </label>
                    </div>
                    <div class="flex items-center">
                        <input id="role_user" name="role" type="radio" value="user" 
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                               {{ old('role', $user->role) === 'user' ? 'checked' : '' }}>
                        <label for="role_user" class="ml-2 block text-sm text-gray-700">
                            Usuário Comum
                        </label>
                    </div>
                </div>
                @error('role')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Estabelecimentos</label>
                <p class="text-sm text-gray-500 mb-2">Selecione os estabelecimentos que este usuário terá acesso:</p>
                <div class="space-y-2 max-h-60 overflow-y-auto p-2 border rounded-md">
                    @forelse($establishments as $establishment)
                        <div class="flex items-center">
                            <input id="establishment_{{ $establishment->id }}" name="establishments[]" type="checkbox" 
                                   value="{{ $establishment->id }}"
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                   {{ in_array($establishment->id, old('establishments', $userEstablishments)) ? 'checked' : '' }}>
                            <label for="establishment_{{ $establishment->id }}" class="ml-2 block text-sm text-gray-700">
                                {{ $establishment->name }}
                            </label>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500">Nenhum estabelecimento cadastrado.</p>
                    @endforelse
                </div>
                @error('establishments')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="flex justify-between items-center">
                <button type="button" 
                        onclick="if(confirm('Tem certeza que deseja remover este usuário?')) { document.getElementById('delete-form').submit(); }" 
                        class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Remover Usuário
                </button>

                <div class="flex space-x-3">
                    <a href="{{ route('super-admin.users.index') }}" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancelar
                    </a>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Atualizar Usuário
                    </button>
                </div>
            </div>
        </form>

        <form id="delete-form" action="{{ route('super-admin.users.destroy', $user) }}" method="POST" class="hidden">
            @csrf
            @method('DELETE')
        </form>
    </div>
</div>
@endsection
