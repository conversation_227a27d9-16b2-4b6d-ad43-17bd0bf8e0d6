@extends('layouts.super-admin')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Gerenciar Estabelecimentos</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ route('super-admin.establishments.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Novo Estabelecimento
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Tipo</th>
                            <th>Proprietário</th>
                            <th>Status</th>
                            <th>Locais</th>
                            <th>Pedidos</th>
                            <th>Criado em</th>
                            <th>A<PERSON><PERSON><PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($establishments as $establishment)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($establishment->type === 'hotel')
                                            <i class="fas fa-hotel me-2 text-primary"></i>
                                        @elseif($establishment->type === 'restaurant')
                                            <i class="fas fa-utensils me-2 text-success"></i>
                                        @elseif($establishment->type === 'cafe')
                                            <i class="fas fa-coffee me-2 text-warning"></i>
                                        @else
                                            <i class="fas fa-store me-2 text-info"></i>
                                        @endif
                                        <div>
                                            <strong>{{ $establishment->name }}</strong>
                                            @if($establishment->address)
                                                <br><small class="text-muted">{{ Str::limit($establishment->address, 30) }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ ucfirst($establishment->type) }}
                                    </span>
                                </td>
                                <td>
                                    @php
                                        $owner = $establishment->users->where('pivot.role', 'owner')->first();
                                    @endphp
                                    {{ $owner ? $owner->name : 'N/A' }}
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" 
                                               {{ $establishment->active ? 'checked' : '' }}
                                               onchange="toggleStatus({{ $establishment->id }})">
                                        <label class="form-check-label">
                                            {{ $establishment->active ? 'Ativo' : 'Inativo' }}
                                        </label>
                                    </div>
                                </td>
                                <td>{{ $establishment->locations_count }}</td>
                                <td>{{ $establishment->orders_count }}</td>
                                <td>{{ $establishment->created_at->format('d/m/Y') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('super-admin.establishments.show', $establishment) }}" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('super-admin.establishments.edit', $establishment) }}" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger" 
                                                onclick="confirmDelete({{ $establishment->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            {{ $establishments->links() }}
        </div>
    </div>
@endsection

@section('scripts')
<script>
    function toggleStatus(establishmentId) {
        const url = "{{ route('super-admin.establishments.toggle-status', ':id') }}".replace(':id', establishmentId);
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erro ao alterar status');
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao alterar status');
        });
    }

    function confirmDelete(establishmentId) {
        if (confirm('Tem certeza que deseja excluir este estabelecimento? Esta ação não pode ser desfeita.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            
            const url = "{{ route('super-admin.establishments.destroy', ':id') }}".replace(':id', establishmentId);
            form.action = url;
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '_token';
            tokenInput.value = '{{ csrf_token() }}';
            
            form.appendChild(methodInput);
            form.appendChild(tokenInput);
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection
