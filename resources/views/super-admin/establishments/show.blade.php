@extends('layouts.super-admin')

@section('content')
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2>Detalhes do Estabelecimento</h2>
            <div>
                <a href="{{ route('super-admin.establishments.edit', $establishment) }}" class="btn btn-warning">Editar</a>
                <form action="{{ route('super-admin.establishments.destroy', $establishment) }}" method="POST" class="d-inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Tem certeza que deseja excluir este estabelecimento?')">Excluir</button>
                </form>
            </div>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <h5>Nome</h5>
                <p class="lead">{{ $establishment->name }}</p>
            </div>
            <div class="mb-3">
                <h5>Endereço</h5>
                <p>{{ $establishment->address }}</p>
            </div>
            <div class="mb-3">
                <h5>Telefone</h5>
                <p>{{ $establishment->phone }}</p>
            </div>
            <div class="mb-3">
                <h5>Horário de Funcionamento</h5>
                <p>{{ $establishment->working_hours }}</p>
            </div>
            <div class="mb-3">
                <h5>Descritivo</h5>
                <p>{{ $establishment->description }}</p>
            </div>
            <div class="mb-3">
                <h5>Imagem</h5>
                <img src="{{ asset('storage/' . $establishment->image) }}" alt="Imagem do Estabelecimento" class="img-fluid">
            </div>
            @if ($establishment->users)
                <div class="mb-3">
                    <h5>Usuário</h5>
                    <p>{{ $establishment->users[0]->name }}</p>
                </div>
            @endif
        </div>
    </div>
@endsection
