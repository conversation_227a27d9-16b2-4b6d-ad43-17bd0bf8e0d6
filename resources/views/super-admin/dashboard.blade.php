@extends('layouts.super-admin')

@section('content')
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
        <h1 class="h2">Dashboard Super Admin</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-download"></i> Exportar
                </button>
            </div>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total de Estabelecimentos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalEstablishments }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Estabelecimentos Ativos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activeEstablishments }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total de Usuários
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalUsers }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Receita Total
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                R$ {{ number_format($totalRevenue, 2, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Estabelecimentos por Tipo -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Estabelecimentos por Tipo</h6>
                </div>
                <div class="card-body">
                    <canvas id="establishmentTypeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Estabelecimentos Recentes -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Estabelecimentos Recentes</h6>
                </div>
                <div class="card-body">
                    @foreach($recentEstablishments as $establishment)
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                @if($establishment->type === 'hotel')
                                    <i class="fas fa-hotel fa-2x text-primary"></i>
                                @elseif($establishment->type === 'restaurant')
                                    <i class="fas fa-utensils fa-2x text-success"></i>
                                @elseif($establishment->type === 'cafe')
                                    <i class="fas fa-coffee fa-2x text-warning"></i>
                                @else
                                    <i class="fas fa-store fa-2x text-info"></i>
                                @endif
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ $establishment->name }}</h6>
                                <small class="text-muted">
                                    {{ ucfirst($establishment->type) }} • 
                                    {{ $establishment->created_at->format('d/m/Y') }}
                                </small>
                            </div>
                            <div>
                                <span class="badge {{ $establishment->active ? 'bg-success' : 'bg-danger' }}">
                                    {{ $establishment->active ? 'Ativo' : 'Inativo' }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Ações Rápidas</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('super-admin.establishments.create') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus mb-2"></i><br>
                                Novo Estabelecimento
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('super-admin.users.create') }}" class="btn btn-success w-100">
                                <i class="fas fa-user-plus mb-2"></i><br>
                                Novo Usuário
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('super-admin.analytics') }}" class="btn btn-info w-100">
                                <i class="fas fa-chart-line mb-2"></i><br>
                                Ver Analytics
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('super-admin.establishments.index') }}" class="btn btn-warning w-100">
                                <i class="fas fa-list mb-2"></i><br>
                                Gerenciar Tudo
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    // Gráfico de Estabelecimentos por Tipo
    const ctx = document.getElementById('establishmentTypeChart').getContext('2d');
    const establishmentData = @json($establishmentsByType);
    
    const labels = establishmentData.map(item => {
        const types = {
            'hotel': 'Hotéis',
            'restaurant': 'Restaurantes', 
            'cafe': 'Cafeterias',
            'bar': 'Bares',
            'food_truck': 'Food Trucks'
        };
        return types[item.type] || item.type;
    });
    
    const data = establishmentData.map(item => item.count);
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    '#4e73df',
                    '#1cc88a', 
                    '#36b9cc',
                    '#f6c23e',
                    '#e74a3b'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
</script>
@endsection
