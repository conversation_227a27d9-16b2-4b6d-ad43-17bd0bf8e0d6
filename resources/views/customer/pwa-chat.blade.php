@extends('layouts.pwa')

@section('title', 'Assistente - ' . $establishment->name)
@section('page-title', 'Assistente')

@section('content')
    <!-- Chat container with full height -->
    <div class="flex flex-col h-full flex-1">
        <!-- Welcome message -->
        <div class="px-4 py-2 bg-[#f3e7e8] border-b border-[#994d51]/20">
            <p class="text-[#1b0e0e] text-base font-normal leading-normal text-center">Bem-vindo! Como posso ajudá-lo hoje?</p>
        </div>

        <!-- Chat messages container - takes remaining space -->
        <div id="chat-messages" class="flex-1 overflow-y-auto px-4 py-4 space-y-4 min-h-0">
            <!-- Initial bot message -->
            <div class="flex items-end gap-3">
                <div
                    class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
                    style='background-image: url("https://via.placeholder.com/40x40/1b0e0e/fcf8f8?text=🤖")'
                ></div>
                <div class="flex flex-1 flex-col gap-1 items-start">
                    <p class="text-[#994d51] text-[13px] font-normal leading-normal max-w-[360px]">Assistente</p>
                    <div class="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#f3e7e8] text-[#1b0e0e]">
                        Olá! Sou seu Assistente. Como posso ajudá-lo hoje? Posso responder perguntas sobre o menu, ajudar com pedidos ou fornecer informações sobre o estabelecimento.
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick action buttons -->
        <div id="quick-actions" class="px-4 py-2 border-t border-[#f3e7e8]">
            <div class="flex flex-wrap gap-2">
                <button onclick="sendQuickMessage('Ver menu')" class="bg-[#f3e7e8] text-[#1b0e0e] px-3 py-2 rounded-full text-sm">
                    📋 Ver menu
                </button>
                <!--<button onclick="sendQuickMessage('Ingredientes')" class="bg-[#f3e7e8] text-[#1b0e0e] px-3 py-2 rounded-full text-sm">
                    🥗 Ingredientes
                </button>
                <button onclick="sendQuickMessage('Recomendações')" class="bg-[#f3e7e8] text-[#1b0e0e] px-3 py-2 rounded-full text-sm">
                    ⭐ Recomendações
                </button>
                <button onclick="sendQuickMessage('Chamar garçom')" class="bg-[#f3e7e8] text-[#1b0e0e] px-3 py-2 rounded-full text-sm">
                    🔔 Chamar garçom
                </button>-->
            </div>
        </div>

        <!-- Chat input - fixed at bottom -->
        <div class="flex items-center px-4 py-3 gap-3 @container border-t border-[#f3e7e8] bg-[#fcf8f8]">
        <label class="flex flex-col min-w-40 h-12 flex-1">
            <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                <input
                    id="chat-input"
                    placeholder="Digite sua mensagem..."
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#1b0e0e] focus:outline-0 focus:ring-0 border-none bg-[#f3e7e8] focus:border-none h-full placeholder:text-[#994d51] px-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                    onkeypress="handleKeyPress(event)"
                />
                <div class="flex border-none bg-[#f3e7e8] items-center justify-center pr-4 rounded-r-xl border-l-0 !pr-2">
                    <div class="flex items-center gap-4 justify-end">
                        <button
                            onclick="sendMessage()"
                            class="min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-8 px-4 bg-[#ea2832] text-[#fcf8f8] text-sm font-medium leading-normal hidden @[480px]:block"
                        >
                            <span class="truncate">Enviar</span>
                        </button>
                    </div>
                </div>
            </div>
        </label>
        </div>
    </div>

    <!-- Hidden file input for image upload -->
    <input type="file" id="image-upload" accept="image/*" class="hidden" onchange="handleImageUpload(event)">
@endsection

@section('scripts')
<script>
    let chatHistory = [];

    // Predefined responses for common questions
    const botResponses = {
        'ver menu': 'Claro! Você pode ver nosso menu completo clicando no ícone "Menu" na parte inferior da tela. Temos várias categorias disponíveis com pratos deliciosos!',
        'menu': 'Nosso menu está disponível na aba "Menu". Lá você encontrará todas as categorias: aperitivos, pratos principais, sobremesas e bebidas.',
        'ingredientes': 'Para ver os ingredientes de qualquer prato, basta clicar no item no menu. Todas as informações nutricionais e ingredientes estão listados lá. Tem alguma alergia específica?',
        'recomendações': 'Nossas especialidades mais populares são: Espetinhos de Frango Grelhado, Espetinhos de Carne Picante e nossos Espetinhos Vegetarianos. Todos são preparados frescos e temperados com nossa mistura especial de ervas!',
        'recomendação': 'Nossas especialidades mais populares são: Espetinhos de Frango Grelhado, Espetinhos de Carne Picante e nossos Espetinhos Vegetarianos. Todos são preparados frescos e temperados com nossa mistura especial de ervas!',
        'chamar garçom': 'Entendi! Vou notificar nossa equipe que você precisa de assistência. Um garçom virá até sua mesa em breve. Posso ajudar com mais alguma coisa enquanto isso?',
        'garçom': 'Vou chamar um garçom para você! Eles estarão na sua mesa em alguns minutos.',
        'ajuda': 'Estou aqui para ajudar! Posso responder sobre o menu, ingredientes, fazer recomendações ou chamar um garçom. O que você gostaria de saber?',
        'preço': 'Os preços estão listados no menu junto com cada item. Se tiver dúvidas sobre algum prato específico, me diga qual e posso ajudar!',
        'preços': 'Todos os preços estão disponíveis no menu. Você pode acessá-lo pela aba "Menu" na parte inferior da tela.',
        'tempo': 'O tempo de preparo varia de acordo com o prato, mas geralmente nossos espetinhos ficam prontos entre 15-20 minutos. Pratos mais elaborados podem levar um pouco mais.',
        'demora': 'Nossos pratos são preparados frescos, então o tempo médio é de 15-20 minutos. Posso verificar com a cozinha se você tiver pressa!',
        'default': 'Desculpe, não entendi completamente sua pergunta. Posso ajudar com informações sobre o menu, ingredientes, recomendações ou chamar um garçom. Pode reformular sua pergunta?'
    };

    function addMessage(message, isUser = false, timestamp = null) {
        const chatContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');

        if (!timestamp) {
            timestamp = new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }

        if (isUser) {
            messageDiv.innerHTML = `
                <div class="flex items-end gap-3 justify-end">
                    <div class="flex flex-1 flex-col gap-1 items-end">
                        <p class="text-[#994d51] text-[13px] font-normal leading-normal max-w-[360px] text-right">Você • ${timestamp}</p>
                        <div class="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#1b0e0e] text-white">
                            ${message}
                        </div>
                    </div>
                    <div class="bg-[#1b0e0e] text-white rounded-full w-10 h-10 shrink-0 flex items-center justify-center text-sm font-bold">
                        👤
                    </div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="flex items-end gap-3">
                    <div
                        class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10 shrink-0"
                        style='background-image: url("https://via.placeholder.com/40x40/1b0e0e/fcf8f8?text=🤖")'
                    ></div>
                    <div class="flex flex-1 flex-col gap-1 items-start">
                        <p class="text-[#994d51] text-[13px] font-normal leading-normal max-w-[360px]">Garçom Virtual • ${timestamp}</p>
                        <div class="text-base font-normal leading-normal flex max-w-[360px] rounded-xl px-4 py-3 bg-[#f3e7e8] text-[#1b0e0e]">
                            ${message}
                        </div>
                    </div>
                </div>
            `;
        }

        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;

        // Store in chat history
        chatHistory.push({ message, isUser, timestamp });
    }

    function getBotResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();

        // Check for keywords in the message
        for (const [key, response] of Object.entries(botResponses)) {
            if (key !== 'default' && lowerMessage.includes(key)) {
                return response;
            }
        }

        return botResponses.default;
    }

    function sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();

        if (!message) return;

        // Add user message
        addMessage(message, true);
        input.value = '';

        // Hide quick actions after first message
        document.getElementById('quick-actions').style.display = 'none';

        // Simulate typing delay
        setTimeout(() => {
            const response = getBotResponse(message);
            addMessage(response, false);
        }, 1000);
    }

    function sendQuickMessage(message) {
        addMessage(message, true);

        // Hide quick actions
        document.getElementById('quick-actions').style.display = 'none';

        setTimeout(() => {
            const response = getBotResponse(message);
            addMessage(response, false);
        }, 1000);
    }

    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }

    function toggleImageUpload() {
        document.getElementById('image-upload').click();
    }

    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            addMessage(`📷 Imagem enviada: ${file.name}`, true);

            setTimeout(() => {
                addMessage('Obrigado pela imagem! Posso ajudar com mais alguma coisa sobre o que você mostrou?', false);
            }, 1000);
        }
    }

    // Auto-scroll to bottom when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const chatContainer = document.getElementById('chat-messages');
        chatContainer.scrollTop = chatContainer.scrollHeight;
    });
</script>
@endsection
