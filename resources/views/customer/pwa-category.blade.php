@extends('layouts.pwa')

@section('title', ucfirst($category) . ' - ' . $establishment->name)
@section('page-title', ucfirst($category))

@section('content')
    <h2 class="text-[#1b0e0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">{{ ucfirst($category) }}</h2>

    @if($type === 'dish' && $items->count() > 0)
        @foreach($items as $dish)
            <a href="{{ route('customer.pwa.item-details', [$establishment, $location, 'dish', $dish->id]) }}" class="flex gap-4 bg-[#fcf8f8] px-4 py-3 border-b border-[#f3e7e8]">
                <div
                    class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-[70px]"
                    style='background-image: url("{{ $dish->image_url ?? 'https://via.placeholder.com/70x70?text=Sem+Imagem' }}")'
                ></div>
                <div class="flex flex-1 flex-col justify-center">
                    <p class="text-[#1b0e0e] text-base font-medium leading-normal">{{ $dish->name }}</p>
                    <p class="text-[#994d51] text-sm font-normal leading-normal">R$ {{ number_format($dish->price, 2, ',', '.') }}</p>
                    <p class="text-[#994d51] text-sm font-normal leading-normal">{{ Str::limit($dish->description, 100) }}</p>
                    @if($dish->ingredients && count($dish->ingredients) > 0)
                        <p class="text-[#994d51] text-xs font-normal leading-normal mt-1">
                            <strong>Ingredientes:</strong> {{ implode(', ', array_slice($dish->ingredients, 0, 3)) }}{{ count($dish->ingredients) > 3 ? '...' : '' }}
                        </p>
                    @endif
                    @if($dish->dietary_restrictions && count($dish->dietary_restrictions) > 0)
                        <div class="flex gap-1 mt-1">
                            @foreach(array_slice($dish->dietary_restrictions, 0, 2) as $restriction)
                                <span class="bg-[#f3e7e8] text-[#994d51] text-xs px-2 py-1 rounded">{{ $restriction }}</span>
                            @endforeach
                        </div>
                    @endif
                </div>
            </a>
        @endforeach
    @endif

    @if($type === 'service' && $items->count() > 0)
        @foreach($items as $service)
            <a href="{{ route('customer.pwa.item-details', [$establishment, $location, 'service', $service->id]) }}" class="flex gap-4 bg-[#fcf8f8] px-4 py-3 border-b border-[#f3e7e8]">
                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-[70px] bg-gray-200 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#994d51" viewBox="0 0 256 256">
                        <path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
                    </svg>
                </div>
                <div class="flex flex-1 flex-col justify-center">
                    <p class="text-[#1b0e0e] text-base font-medium leading-normal">{{ $service->name }}</p>
                    <p class="text-[#994d51] text-sm font-normal leading-normal">
                        @if($service->price)
                            R$ {{ number_format($service->price, 2, ',', '.') }}
                        @else
                            Gratuito
                        @endif
                    </p>
                    <p class="text-[#994d51] text-sm font-normal leading-normal">{{ Str::limit($service->description, 100) }}</p>
                    @if($service->duration)
                        <p class="text-[#994d51] text-xs font-normal leading-normal mt-1">
                            <strong>Duração:</strong> {{ $service->duration }} minutos
                        </p>
                    @endif
                </div>
            </a>
        @endforeach
    @endif

    @if($items->count() === 0)
        <div class="flex flex-col items-center justify-center py-16 px-4">
            <div class="text-[#994d51] mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 256 256">
                    <path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
                </svg>
            </div>
            <h3 class="text-[#1b0e0e] text-lg font-bold mb-2">Nenhum item encontrado</h3>
            <p class="text-[#994d51] text-center">Não há {{ $type === 'dish' ? 'pratos' : 'serviços' }} disponíveis nesta categoria no momento.</p>
        </div>
    @endif
@endsection
