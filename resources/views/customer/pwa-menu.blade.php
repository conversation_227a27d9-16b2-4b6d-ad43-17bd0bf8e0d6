@php use Illuminate\Support\Str; @endphp
@extends('layouts.pwa')

@section('title', 'Menu - ' . $establishment->name)
@section('page-title', $establishment->name . ' - Menu')

@section('content')
    <!-- Featured Items Carousel -->
    @if($dishes->count() > 0)
        <h1 class="text-center mb-1 mt-1">Mais pedidos</h1>
        <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
            <div class="flex items-stretch p-4 gap-3">
                @foreach($dishes as $dish)
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                        <a href="{{ route('customer.pwa.item-details', [$establishment, $location, 'dish', $dish->id]) }}">
                            <img src="{{ $dish->image_url ?? 'https://placehold.co/300x300' }}" alt="{{ $dish->name }}" class="object-cover rounded-xl aspect-square">
                            <div>
                                <p class="text-[#1b0e0e] text-base font-medium leading-normal">{{ $dish->name }}</p>
                                <p class="text-[#994d51] text-sm font-normal leading-normal">{{ Str::limit($dish->description, 80) }}</p>
                                <p class="text-[#1b0e0e] text-sm font-bold mt-1">R$ {{ number_format($dish->price, 2, ',', '.') }}</p>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Categories Section -->
    <h2 class="text-[#1b0e0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Categorias</h2>
    <div class="grid grid-cols-2 gap-3 p-4">
        @php
            $categories = collect();
            if($dishes->count() > 0) {
                $categories = $categories->merge($dishes->groupBy('category')->keys()->map(function($category) {
                    return [
                        'name' => ucfirst($category),
                        'type' => 'dish',
                        'slug' => Str::slug($category),
                        'image' => 'https://placehold.co/75x75'
                    ];
                }));
            }
            if($services->count() > 0) {
                $categories = $categories->merge($services->groupBy('category')->keys()->map(function($category) {
                    return [
                        'name' => ucfirst($category),
                        'type' => 'service',
                        'slug' => Str::slug($category),
                        'image' => 'https://placehold.co/75x75'
                    ];
                }));
            }
        @endphp

        @foreach($categories as $category)
            <a href="{{ route('customer.pwa.category', [$establishment, $location, $category['type'], $category['slug']]) }}" class="flex flex-col gap-3 pb-3">
                <div
                    class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                    style='background-image: url("{{ $category['image'] }}")'
                ></div>
                <p class="text-[#1b0e0e] text-base font-medium leading-normal">{{ $category['name'] }}</p>
            </a>
        @endforeach
    </div>


@endsection


