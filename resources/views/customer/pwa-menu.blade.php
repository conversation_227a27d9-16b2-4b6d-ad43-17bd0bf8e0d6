@php use Illuminate\Support\Str; @endphp
@extends('layouts.pwa')

@section('title', 'Menu - ' . $establishment->name)
@section('page-title', $establishment->name . ' - Menu')

@section('content')
    <!-- Featured Items Carousel -->
    @if($dishes->count() > 0)
        <h1 class="text-center mb-1 mt-1">Mais pedidos</h1>
        <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
            <div class="flex items-stretch p-4 gap-3">
                @foreach($dishes as $dish)
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                        <a href="{{ route('customer.pwa.item-details', [$establishment, $location, 'dish', $dish->id]) }}">
                            <img src="{{ $dish->image_url ?? 'https://placehold.co/300x300' }}" alt="{{ $dish->name }}" class="object-cover rounded-xl aspect-square">
                            <div>
                                <p class="text-[#1b0e0e] text-base font-medium leading-normal">{{ $dish->name }}</p>
                                <p class="text-[#994d51] text-sm font-normal leading-normal">{{ Str::limit($dish->description, 80) }}</p>
                                <p class="text-[#1b0e0e] text-sm font-bold mt-1">R$ {{ number_format($dish->price, 2, ',', '.') }}</p>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Categories Section -->
    <h2 class="text-[#1b0e0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Categorias</h2>
    <div class="grid grid-cols-2 gap-3 p-4">
        @php
            $categories = collect();
            if($dishes->count() > 0) {
                $categories = $categories->merge($dishes->groupBy('category')->keys()->map(function($category) {
                    return [
                        'name' => ucfirst($category),
                        'type' => 'dish',
                        'slug' => Str::slug($category),
                        'image' => 'https://placehold.co/75x75'
                    ];
                }));
            }
            if($services->count() > 0) {
                $categories = $categories->merge($services->groupBy('category')->keys()->map(function($category) {
                    return [
                        'name' => ucfirst($category),
                        'type' => 'service',
                        'slug' => Str::slug($category),
                        'image' => 'https://placehold.co/75x75'
                    ];
                }));
            }
        @endphp

        @foreach($categories as $category)
            <a href="{{ route('customer.pwa.category', [$establishment, $location, $category['type'], $category['slug']]) }}" class="flex flex-col gap-3 pb-3">
                <div
                    class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                    style='background-image: url("{{ $category['image'] }}")'
                ></div>
                <p class="text-[#1b0e0e] text-base font-medium leading-normal">{{ $category['name'] }}</p>
            </a>
        @endforeach
    </div>


@endsection

@section('scripts')
<script>
    // PWA Session Management - Initial QR Code Access Point
    let sessionToken = null;
    let sessionExpiry = null;

    // Initialize session when page loads (this is the entry point from QR code scan)
    document.addEventListener('DOMContentLoaded', function() {
        initializePWASession();
    });

    async function initializePWASession() {
        // Check if we already have a valid session from this browser session
        const storedToken = sessionStorage.getItem('pwa_session_token');
        const storedExpiry = sessionStorage.getItem('pwa_session_expiry');

        if (storedToken && storedExpiry) {
            const expiryTime = new Date(storedExpiry);
            const now = new Date();

            // If session is still valid (with 1 minute buffer), use it
            if (expiryTime > new Date(now.getTime() + 60000)) {
                sessionToken = storedToken;
                sessionExpiry = expiryTime;
                console.log('Sessão PWA ativa encontrada');
                return;
            } else {
                // Clear expired session
                sessionStorage.removeItem('pwa_session_token');
                sessionStorage.removeItem('pwa_session_expiry');
            }
        }

        // This is initial access via QR code - start new session with geolocation
        console.log('Iniciando nova sessão PWA...');
        await startNewSession();
    }

    async function startNewSession() {
        try {
            console.log('🔐 Iniciando sessão PWA via QR Code...');

            // Step 1: Request geolocation (mandatory for PWA access)
            if (!navigator.geolocation) {
                throw new Error('Geolocalização não suportada pelo navegador');
            }

            console.log('📍 Solicitando localização...');
            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 30000
                });
            });

            console.log('✅ Localização obtida, validando com servidor...');

            // Step 2: Send location to backend for validation and session creation
            const response = await fetch('/api/customer/session/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    establishment_id: {{ $establishment->id }},
                    location_id: {{ $location->id }},
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Erro ao validar localização');
            }

            const sessionData = await response.json();

            // Step 3: Store session data in sessionStorage
            sessionToken = sessionData.session_token;
            sessionExpiry = new Date(sessionData.expires_at);

            sessionStorage.setItem('pwa_session_token', sessionToken);
            sessionStorage.setItem('pwa_session_expiry', sessionExpiry.toISOString());

            console.log('🎉 Sessão PWA iniciada com sucesso!');
            console.log('Token expira em:', sessionExpiry.toLocaleString());

        } catch (error) {
            console.error('❌ Erro ao iniciar sessão PWA:', error);

            // Show user-friendly error messages
            if (error.message.includes('too far') || error.message.includes('far from')) {
                alert('❌ Você está muito longe do estabelecimento para fazer pedidos.\n\nPor favor, aproxime-se da mesa e tente novamente.');
                return;
            } else if (error.message.includes('Geolocalização') || error.name === 'GeolocationPositionError') {
                alert('📍 É necessário permitir o acesso à localização para usar o sistema de pedidos.\n\nPor favor, permita a localização e recarregue a página.');
                return;
            } else if (error.message.includes('location not configured')) {
                alert('⚙️ Estabelecimento não configurado corretamente.\n\nEntre em contato com o atendimento.');
                return;
            } else {
                alert('❌ Erro ao conectar com o servidor.\n\nVerifique sua conexão e tente novamente.');
                console.warn('Session start failed:', error.message);
            }
        }
    }

    // Function to get current session token (used by other pages)
    function getPWASessionToken() {
        const storedToken = sessionStorage.getItem('pwa_session_token');
        const storedExpiry = sessionStorage.getItem('pwa_session_expiry');

        if (storedToken && storedExpiry) {
            const expiryTime = new Date(storedExpiry);
            const now = new Date();

            // If session is still valid (with 1 minute buffer), return it
            if (expiryTime > new Date(now.getTime() + 60000)) {
                return storedToken;
            }
        }

        return null;
    }

    // Function to refresh session if needed (used by other pages)
    async function refreshPWASessionIfNeeded() {
        const currentToken = getPWASessionToken();
        if (!currentToken) {
            await startNewSession();
            return getPWASessionToken();
        }
        return currentToken;
    }

    // Make functions available globally
    window.getPWASessionToken = getPWASessionToken;
    window.refreshPWASessionIfNeeded = refreshPWASessionIfNeeded;
</script>
@endsection
