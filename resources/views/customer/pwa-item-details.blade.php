@extends('layouts.pwa')

@section('title', $item->name . ' - ' . $establishment->name)
@section('page-title', 'Menu')
@section('content')
    <div class="@container">
        <div class="@[480px]:px-4 @[480px]:py-3">
            <div
                class="w-full bg-center bg-no-repeat bg-cover flex flex-col justify-end overflow-hidden bg-[#fcf8f8] @[480px]:rounded-xl min-h-[218px]"
                style='background-image: url("{{ $item->image_url ?? 'https://placehold.co/400x300/' }}")'
            ></div>
        </div>
    </div>

    <h1 class="text-[#1b0e0e] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 text-left pb-3 pt-5">{{ $item->name }}</h1>
    <p class="text-[#1b0e0e] text-base font-normal leading-normal pb-3 pt-1 px-4">{{ $item->description }}</p>

    @if($item->dietary_restrictions && count($item->dietary_restrictions) > 0)
        <div class="px-4 pb-3">
            <div class="flex flex-wrap gap-2">
                @foreach($item->dietary_restrictions as $restriction)
                    <span class="bg-[#f3e7e8] text-[#994d51] text-xs px-2 py-1 rounded-full">{{ $restriction }}</span>
                @endforeach
            </div>
        </div>
    @endif

    <h3 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Preço</h3>
    <p class="text-[#1b0e0e] text-base font-normal leading-normal pb-3 pt-1 px-4">R$ {{ number_format($item->price, 2, ',', '.') }}</p>

    <h3 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Quantidade</h3>
    <div class="flex items-center gap-4 bg-[#fcf8f8] px-4 min-h-14 justify-between">
        <p class="text-[#1b0e0e] text-base font-normal leading-normal flex-1 truncate">Quantidade</p>
        <div class="shrink-0">
            <div class="flex items-center gap-2 text-[#1b0e0e]">
                <button onclick="updateQuantity(-1)" class="text-base font-medium leading-normal flex h-7 w-7 items-center justify-center rounded-full bg-[#f3e7e8] cursor-pointer">-</button>
                <input
                    id="quantity-input"
                    class="text-base font-medium leading-normal w-8 p-0 text-center bg-transparent focus:outline-0 focus:ring-0 focus:border-none border-none [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                    type="number"
                    value="1"
                    min="1"
                    max="99"
                    onchange="validateQuantity()"
                />
                <button onclick="updateQuantity(1)" class="text-base font-medium leading-normal flex h-7 w-7 items-center justify-center rounded-full bg-[#f3e7e8] cursor-pointer">+</button>
            </div>
        </div>
    </div>

    @if($item->ingredients && count($item->ingredients) > 0)
        @php
            // Separar ingredientes obrigatórios e opcionais
            // Para este exemplo, vamos considerar os últimos 3 ingredientes como opcionais
            $allIngredients = $item->ingredients;
            $requiredIngredients = array_slice($allIngredients, 0, -3);
            $optionalIngredients = array_slice($allIngredients, -3);
        @endphp

        @if(count($requiredIngredients) > 0)
            <h3 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Ingredientes Inclusos</h3>
            <div class="px-4 pb-3">
                <p class="text-[#994d51] text-sm">{{ implode(', ', $requiredIngredients) }}</p>
            </div>
        @endif

        @if(count($optionalIngredients) > 0)
            <h3 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Ingredientes Opcionais</h3>
            @foreach($optionalIngredients as $ingredient)
                <div class="flex items-center gap-4 bg-[#fcf8f8] px-4 min-h-14 justify-between">
                    <p class="text-[#1b0e0e] text-base font-normal leading-normal flex-1 truncate">{{ $ingredient }}</p>
                    <div class="shrink-0">
                        <label class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full border-none bg-[#f3e7e8] p-0.5 has-[:checked]:justify-end has-[:checked]:bg-[#e82630]">
                            <div class="h-full w-[27px] rounded-full bg-white transition-all duration-200" style="box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 8px, rgba(0, 0, 0, 0.06) 0px 3px 1px;"></div>
                            <input
                                type="checkbox"
                                class="invisible absolute ingredient-checkbox"
                                data-ingredient="{{ $ingredient }}"
                                checked
                            />
                        </label>
                    </div>
                </div>
            @endforeach
        @endif
    @endif

    @if($item->customizable_options && count($item->customizable_options) > 0)
        <h3 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Opções de Customização</h3>
        @foreach($item->customizable_options as $option)
            <div class="flex items-center gap-4 bg-[#fcf8f8] px-4 min-h-14 justify-between">
                <p class="text-[#1b0e0e] text-base font-normal leading-normal flex-1 truncate">{{ $option }}</p>
                <div class="shrink-0">
                    <label class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full border-none bg-[#f3e7e8] p-0.5 has-[:checked]:justify-end has-[:checked]:bg-[#e82630]">
                        <div class="h-full w-[27px] rounded-full bg-white transition-all duration-200" style="box-shadow: rgba(0, 0, 0, 0.15) 0px 3px 8px, rgba(0, 0, 0, 0.06) 0px 3px 1px;"></div>
                        <input
                            type="checkbox"
                            class="invisible absolute customization-checkbox"
                            data-option="{{ $option }}"
                        />
                    </label>
                </div>
            </div>
        @endforeach
    @endif

    <!-- Add to Cart Button -->
    <div class="flex px-4 py-3">
        <button
            onclick="addItemToCart()"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 px-5 flex-1 bg-[#e82630] text-[#fcf8f8] text-base font-bold leading-normal tracking-[0.015em]"
        >
            <span class="truncate">Adicionar ao Carrinho</span>
        </button>
    </div>
@endsection

@section('scripts')
<script>
    let currentQuantity = 1;
    const itemData = {
        type: '{{ $type }}',
        id: {{ $item->id }},
        name: '{{ addslashes($item->name) }}',
        price: {{ $item->price }},
        image_url: '{{ $item->image_url ?? '' }}'
    };

    function updateQuantity(change) {
        const input = document.getElementById('quantity-input');
        const newQuantity = Math.max(1, Math.min(99, currentQuantity + change));

        if (newQuantity !== currentQuantity) {
            currentQuantity = newQuantity;
            input.value = currentQuantity;
        }
    }

    function validateQuantity() {
        const input = document.getElementById('quantity-input');
        const value = parseInt(input.value) || 1;
        currentQuantity = Math.max(1, Math.min(99, value));
        input.value = currentQuantity;
    }

    function getSelectedIngredients() {
        const checkboxes = document.querySelectorAll('.ingredient-checkbox');
        const selected = [];

        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selected.push(checkbox.dataset.ingredient);
            }
        });

        return selected;
    }

    function getSelectedCustomizations() {
        const checkboxes = document.querySelectorAll('.customization-checkbox');
        const selected = [];

        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selected.push(checkbox.dataset.option);
            }
        });

        return selected;
    }

    function addItemToCart() {
        const selectedIngredients = getSelectedIngredients();
        const selectedCustomizations = getSelectedCustomizations();

        // Create item object with customizations
        const cartItem = {
            ...itemData,
            quantity: currentQuantity,
            selectedIngredients: selectedIngredients,
            customizations: selectedCustomizations,
            // Create a unique identifier for this specific configuration
            configId: Date.now() + Math.random()
        };

        // Check if exact same configuration already exists in cart
        const existingItemIndex = cart.findIndex(item =>
            item.type === cartItem.type &&
            item.id === cartItem.id &&
            JSON.stringify(item.selectedIngredients) === JSON.stringify(cartItem.selectedIngredients) &&
            JSON.stringify(item.customizations) === JSON.stringify(cartItem.customizations)
        );

        if (existingItemIndex !== -1) {
            // Update quantity of existing item
            cart[existingItemIndex].quantity += cartItem.quantity;
        } else {
            // Add new item to cart
            cart.push(cartItem);
        }

        saveCart();
        updateCartDisplay();

        // Show success message and redirect
        showSuccessMessage();
    }

    function showSuccessMessage() {
        // Create and show a temporary success message
        const message = document.createElement('div');
        message.className = 'fixed top-4 left-4 right-4 bg-green-500 text-white p-4 rounded-xl z-50 text-center';
        message.textContent = `${currentQuantity}x ${itemData.name} adicionado ao carrinho!`;
        document.body.appendChild(message);

        // Remove message and redirect after 2 seconds
        setTimeout(() => {
            message.remove();
            history.back();
        }, 2000);
    }

    // Initialize quantity input
    document.getElementById('quantity-input').addEventListener('input', validateQuantity);
</script>
@endsection
