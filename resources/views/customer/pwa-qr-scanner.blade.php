@extends('layouts.pwa')

@section('title', 'Escanear QR Code - ' . $establishment->name)
@section('page-title', 'Escanear QR Code')

@section('content')
    <p class="text-[#1b0e0e] text-base font-normal leading-normal pb-3 pt-1 px-4 text-center">
        <PERSON><PERSON><PERSON> o código QR dentro do quadro para finalizar sua transação.
    </p>

    <div class="flex w-full grow bg-[#fcf8f8] @container p-4">
        <div class="w-full gap-1 overflow-hidden bg-[#fcf8f8] @[480px]:gap-2 aspect-[3/2] rounded-xl flex">
            <!-- Camera preview will be inserted here -->
            <div id="camera-container" class="w-full bg-center bg-no-repeat bg-cover aspect-auto rounded-none flex-1 bg-gray-800 flex items-center justify-center relative">
                <video id="camera-preview" class="w-full h-full object-cover rounded-xl" autoplay muted playsinline></video>

                <!-- QR Code overlay frame -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="relative">
                        <!-- QR scanning frame -->
                        <div class="w-64 h-64 border-4 border-white rounded-xl relative">
                            <!-- Corner indicators -->
                            <div class="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-[#ea2832] rounded-tl-xl"></div>
                            <div class="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-[#ea2832] rounded-tr-xl"></div>
                            <div class="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-[#ea2832] rounded-bl-xl"></div>
                            <div class="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-[#ea2832] rounded-br-xl"></div>

                            <!-- Scanning line animation -->
                            <div id="scan-line" class="absolute left-0 right-0 h-1 bg-[#ea2832] opacity-75 animate-pulse"></div>
                        </div>
                    </div>
                </div>

                <!-- Fallback image when camera is not available -->
                <div id="camera-fallback" class="hidden w-full h-full bg-center bg-no-repeat bg-cover rounded-xl flex items-center justify-center"
                     style='background-image: url("https://via.placeholder.com/400x300/1b0e0e/fcf8f8?text=Camera+Indisponível")'>
                    <div class="text-center text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 256 256" class="mx-auto mb-2">
                            <path d="M208,56H180.28L166.65,35.56A8,8,0,0,0,160,32H96a8,8,0,0,0-6.65,3.56L75.72,56H48A24,24,0,0,0,24,80V192a24,24,0,0,0,24,24H208a24,24,0,0,0,24-24V80A24,24,0,0,0,208,56Zm8,136a8,8,0,0,1-8,8H48a8,8,0,0,1-8-8V80a8,8,0,0,1,8-8H80a8,8,0,0,0,6.65-3.56L100.28,48h55.44L169.35,68.44A8,8,0,0,0,176,72h32a8,8,0,0,1,8,8ZM128,88a44,44,0,1,0,44,44A44.05,44.05,0,0,0,128,88Zm0,72a28,28,0,1,1,28-28A28,28,0,0,1,128,160Z"></path>
                        </svg>
                        <p class="text-sm">Câmera não disponível</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status messages -->
    <div id="scan-status" class="px-4 py-2 text-center">
        <p id="status-message" class="text-[#994d51] text-sm">Posicione o QR code dentro do quadro</p>
    </div>

    <!-- Customer name input -->
    <div id="customer-info" class="px-4 py-3">
        <div class="bg-[#f3e7e8] rounded-xl p-4">
            <h4 class="text-[#1b0e0e] font-bold mb-2">Informações do {{ $establishment->type === 'hotel' ? 'Hóspede' : 'Cliente' }}</h4>
            <input
                type="text"
                id="customer-name"
                placeholder="Digite seu nome"
                class="w-full px-4 py-3 rounded-xl border-none bg-white text-[#1b0e0e] placeholder-[#994d51] focus:outline-none focus:ring-2 focus:ring-[#1b0e0e] mb-3"
                required
            >
            <textarea
                id="special-requests"
                placeholder="Observações especiais (opcional)"
                rows="2"
                class="w-full px-4 py-3 rounded-xl border-none bg-white text-[#1b0e0e] placeholder-[#994d51] focus:outline-none focus:ring-2 focus:ring-[#1b0e0e]"
            ></textarea>
        </div>
    </div>

    <!-- Manual input fallback -->
    <div id="manual-input" class="px-4 py-3 hidden">
        <div class="bg-[#f3e7e8] rounded-xl p-4">
            <h4 class="text-[#1b0e0e] font-bold mb-2">Inserir código manualmente</h4>
            <input
                type="text"
                id="manual-qr-code"
                placeholder="Digite o código QR"
                class="w-full px-4 py-3 rounded-xl border-none bg-white text-[#1b0e0e] placeholder-[#994d51] focus:outline-none focus:ring-2 focus:ring-[#1b0e0e] mb-3"
            >
            <button onclick="processManualCode()" class="w-full bg-[#1b0e0e] text-white py-3 rounded-xl font-medium">
                Confirmar Código
            </button>
        </div>
    </div>

    <!-- Action buttons -->
    <div class="flex px-4 py-3 gap-3">
        <button
            id="scan-button"
            onclick="startScanning()"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 flex-1 bg-[#ea2832] text-[#fcf8f8] text-base font-bold leading-normal tracking-[0.015em]"
        >
            <span class="truncate">Escanear</span>
        </button>

        <button
            onclick="toggleManualInput()"
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-[#f3e7e8] text-[#1b0e0e] text-base font-bold leading-normal tracking-[0.015em]"
        >
            <span class="truncate">Manual</span>
        </button>
    </div>

    <!-- Geolocation Validation Overlay -->
    <div id="geo-validation-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-[#fcf8f8] rounded-xl max-w-sm w-full p-6 text-center">
            <div class="text-[#994d51] mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 256 256" class="mx-auto">
                    <path d="M128,64a40,40,0,1,0,40,40A40,40,0,0,0,128,64Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,128ZM176,16H80A16,16,0,0,0,64,32V224a16,16,0,0,0,16,16h96a16,16,0,0,0,16-16V32A16,16,0,0,0,176,16ZM80,32h96V224H80Z"></path>
                </svg>
            </div>
            <h3 class="text-[#1b0e0e] text-lg font-bold mb-2">Problema de localização</h3>
            <p class="text-[#994d51] text-sm mb-6" id="geo-error-message">
                Você se afastou muito da mesa. Retorne à sua mesa para finalizar o pedido.
            </p>
            <div class="flex flex-col gap-3">
                <button
                    onclick="retryOrderWithGeo()"
                    class="w-full bg-[#1b0e0e] text-white py-3 rounded-xl font-medium"
                    id="retry-order-button"
                >
                    Tentar novamente
                </button>
                <button
                    onclick="hideGeoOverlay()"
                    class="w-full bg-[#f3e7e8] text-[#994d51] py-3 rounded-xl font-medium"
                >
                    Cancelar
                </button>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    let stream = null;
    let isScanning = false;
    let scanInterval = null;

    // Initialize camera when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeCamera();
    });

    async function initializeCamera() {
        try {
            const constraints = {
                video: {
                    facingMode: 'environment', // Use back camera
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            };

            stream = await navigator.mediaDevices.getUserMedia(constraints);
            const video = document.getElementById('camera-preview');
            video.srcObject = stream;

            updateStatus('Câmera inicializada. Posicione o QR code no quadro.', 'success');

        } catch (error) {
            console.error('Error accessing camera:', error);
            showCameraFallback();
            updateStatus('Não foi possível acessar a câmera. Use a opção manual.', 'error');
        }
    }

    function showCameraFallback() {
        document.getElementById('camera-preview').style.display = 'none';
        document.getElementById('camera-fallback').classList.remove('hidden');
    }

    function startScanning() {
        if (isScanning) {
            stopScanning();
            return;
        }

        if (!stream) {
            updateStatus('Câmera não disponível. Use a opção manual.', 'error');
            return;
        }

        isScanning = true;
        document.getElementById('scan-button').innerHTML = '<span class="truncate">Parar</span>';
        updateStatus('Escaneando... Mantenha o QR code no quadro.', 'scanning');

        // Simulate QR code scanning (in a real implementation, you'd use a QR code library)
        scanInterval = setInterval(() => {
            // This is where you'd implement actual QR code detection
            // For now, we'll simulate a successful scan after a few seconds
            if (Math.random() > 0.95) { // 5% chance per interval
                const mockQRCode = 'table-{{ $location->qr_code }}';
                processQRCode(mockQRCode);
            }
        }, 100);
    }

    function stopScanning() {
        isScanning = false;
        if (scanInterval) {
            clearInterval(scanInterval);
            scanInterval = null;
        }
        document.getElementById('scan-button').innerHTML = '<span class="truncate">Escanear</span>';
        updateStatus('Escaneamento parado.', 'info');
    }

    function processQRCode(qrCode) {
        stopScanning();
        updateStatus('QR Code detectado! Processando...', 'success');

        // Validate QR code format
        if (qrCode.includes('{{ $location->qr_code }}')) {
            updateStatus('QR Code válido! Finalizando pedido...', 'success');

            // Process the order
            setTimeout(() => {
                finalizeOrder(qrCode);
            }, 1000);
        } else {
            updateStatus('QR Code inválido. Tente novamente.', 'error');
        }
    }

    function processManualCode() {
        const manualCode = document.getElementById('manual-qr-code').value.trim();
        if (!manualCode) {
            updateStatus('Por favor, digite um código válido.', 'error');
            return;
        }

        processQRCode(manualCode);
    }

    function toggleManualInput() {
        const manualInput = document.getElementById('manual-input');
        manualInput.classList.toggle('hidden');
    }

    async function finalizeOrder(qrCode) {
        if (cart.length === 0) {
            updateStatus('Carrinho vazio. Adicione itens antes de finalizar.', 'error');
            return;
        }

        // Validate customer name
        const customerName = document.getElementById('customer-name').value.trim();
        if (!customerName) {
            updateStatus('Por favor, digite seu nome antes de continuar.', 'error');
            return;
        }

        try {
            // Step 1: Get stored session token
            updateStatus('🔐 Verificando sessão...', 'info');

            let currentSessionToken = sessionStorage.getItem('pwa_session_token');
            const sessionExpiry = sessionStorage.getItem('pwa_session_expiry');

            if (!currentSessionToken || !sessionExpiry) {
                throw new Error('Sessão não encontrada. Por favor, acesse novamente via QR Code da mesa.');
            }

            // Check if session is still valid
            const expiryTime = new Date(sessionExpiry);
            const now = new Date();

            if (expiryTime <= new Date(now.getTime() + 60000)) {
                throw new Error('Sessão expirada. Por favor, acesse novamente via QR Code da mesa.');
            }

            // Step 2: Get current location for order validation
            updateStatus('📍 Validando localização atual...', 'info');

            if (!navigator.geolocation) {
                throw new Error('Geolocalização não suportada pelo navegador');
            }

            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    enableHighAccuracy: true,
                    timeout: 15000,
                    maximumAge: 30000
                });
            });

            // Step 3: Send order with session token + current location + QR validation
            updateStatus('🚀 Enviando pedido...', 'info');

            const specialRequests = document.getElementById('special-requests').value.trim();

            const orderPayload = {
                session_token: currentSessionToken,
                qr_code_scanned: qrCode,
                customer_name: customerName,
                special_requests: specialRequests,
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                items: cart
            };

            console.log('📦 Enviando pedido:', orderPayload);

            const orderResponse = await fetch('/api/customer/order/place', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(orderPayload)
            });

            if (!orderResponse.ok) {
                const errorData = await orderResponse.json();

                // Handle specific error cases
                if (orderResponse.status === 422 && errorData.error) {
                    if (errorData.error.includes('session has expired')) {
                        throw new Error('Sessão expirada. Por favor, acesse novamente via QR Code da mesa.');
                    } else if (errorData.error.includes('QR Code') && errorData.error.includes('does not match')) {
                        throw new Error('QR Code não confere com a mesa da sessão. Verifique se está escaneando o QR Code correto.');
                    } else if (errorData.error.includes('too far')) {
                        throw new Error('Você se afastou muito da mesa. Por favor, retorne à sua mesa para finalizar o pedido.');
                    }
                }

                throw new Error(errorData.error || 'Erro ao enviar pedido');
            }

            const orderData = await orderResponse.json();

            updateStatus('Pedido enviado com sucesso!', 'success');

            // Clear cart and redirect
            cart = [];
            updateCartDisplay();

            setTimeout(() => {
                window.location.href = '{{ route("customer.pwa.order-confirmation", [$establishment, $location]) }}' + '/' + orderData.order_id;
            }, 2000);

        } catch (error) {
            console.error('❌ Erro ao finalizar pedido:', error);

            // Show specific error messages
            let errorMessage = error.message;

            if (error.message.includes('Sessão não encontrada') || error.message.includes('Sessão expirada')) {
                errorMessage = '🔐 ' + error.message;
            } else if (error.message.includes('QR Code')) {
                errorMessage = '📱 ' + error.message;
            } else if (error.message.includes('muito longe') || error.message.includes('too far')) {
                errorMessage = '📍 ' + error.message;
            } else if (error.message.includes('Geolocalização')) {
                errorMessage = '📍 ' + error.message;
            } else {
                errorMessage = '❌ ' + (error.message || 'Erro ao processar pedido. Tente novamente.');
            }

            updateStatus(errorMessage, 'error');
        }
    }

    function updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status-message');
        statusElement.textContent = message;

        // Update color based on type
        statusElement.className = 'text-sm ';
        switch (type) {
            case 'success':
                statusElement.className += 'text-green-600';
                break;
            case 'error':
                statusElement.className += 'text-red-600';
                break;
            case 'scanning':
                statusElement.className += 'text-[#ea2832]';
                break;
            default:
                statusElement.className += 'text-[#994d51]';
        }
    }

    // Cleanup when leaving the page
    window.addEventListener('beforeunload', function() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
        stopScanning();
    });

    // Handle back button
    function goBack() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
        stopScanning();
        history.back();
    }

    // Override the back button behavior
    document.querySelector('[onclick="history.back()"]')?.setAttribute('onclick', 'goBack()');
</script>
@endsection
