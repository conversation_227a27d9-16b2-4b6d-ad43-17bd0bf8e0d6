<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $establishment->name ?? 'Estabelecimento' }} - Painel Administrativo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .establishment-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        @if(isset($establishment))
                            @if($establishment->type === 'hotel')
                                <i class="fas fa-hotel fa-2x text-white mb-2"></i>
                            @elseif($establishment->type === 'restaurant')
                                <i class="fas fa-utensils fa-2x text-white mb-2"></i>
                            @elseif($establishment->type === 'cafe')
                                <i class="fas fa-coffee fa-2x text-white mb-2"></i>
                            @else
                                <i class="fas fa-store fa-2x text-white mb-2"></i>
                            @endif
                            <h6 class="text-white">{{ Str::limit($establishment->name, 20) }}</h6>
                            <small class="text-white-50">{{ ucfirst($establishment->type) }}</small>
                        @endif
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('establishment.dashboard') ? 'active' : '' }}" 
                               href="{{ route('establishment.dashboard', $establishment->id ?? 1) }}">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('establishment.orders.*') ? 'active' : '' }}" 
                               href="{{ route('establishment.orders.index', $establishment->id ?? 1) }}">
                                <i class="fas fa-shopping-cart me-2"></i>Pedidos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('establishment.locations.*') ? 'active' : '' }}" 
                               href="{{ route('establishment.locations.index', $establishment->id ?? 1) }}">
                                @if(isset($establishment) && $establishment->type === 'hotel')
                                    <i class="fas fa-bed me-2"></i>Quartos
                                @else
                                    <i class="fas fa-chair me-2"></i>Mesas
                                @endif
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('establishment.dishes.*') ? 'active' : '' }}" 
                               href="{{ route('establishment.dishes.index', $establishment->id ?? 1) }}">
                                <i class="fas fa-utensils me-2"></i>Cardápio
                            </a>
                        </li>
                        @if(isset($establishment) && $establishment->type === 'hotel')
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('establishment.services.*') ? 'active' : '' }}" 
                               href="{{ route('establishment.services.index', $establishment->id ?? 1) }}">
                                <i class="fas fa-concierge-bell me-2"></i>Serviços
                            </a>
                        </li>
                        @endif
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('establishment.analytics') ? 'active' : '' }}" 
                               href="{{ route('establishment.analytics', $establishment->id ?? 1) }}">
                                <i class="fas fa-chart-bar me-2"></i>Analytics
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="fas fa-sign-out-alt me-2"></i>Sair
                            </a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                @csrf
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @yield('scripts')
</body>
</html>
