<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $establishment->name ?? 'Estabelecimento' }} - {{ $location->display_name ?? '' }}</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />
    <meta name="theme-color" content="#1b0e0e">
    <link rel="manifest" href="/manifest.json">
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        body {
            font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark establishment-header">
        <div class="container">
            <a class="navbar-brand" href="#">
                @if(isset($establishment))
                    @if($establishment->type === 'hotel')
                        <i class="fas fa-hotel me-2"></i>
                    @elseif($establishment->type === 'restaurant')
                        <i class="fas fa-utensils me-2"></i>
                    @elseif($establishment->type === 'cafe')
                        <i class="fas fa-coffee me-2"></i>
                    @else
                        <i class="fas fa-store me-2"></i>
                    @endif
                    {{ $establishment->name }}
                @else
                    <i class="fas fa-store me-2"></i>Estabelecimento
                @endif
            </a>
            @isset($location)
                <span class="navbar-text text-white">
                    {{ $location->display_name }}
                </span>
            @endisset
        </div>
    </nav>

    <main class="container my-4">
        @yield('content')
    </main>

    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="cart-summary" class="card shadow" style="display: none;">
            <div class="card-body">
                <h6 class="card-title">Carrinho</h6>
                <div id="cart-items"></div>
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>Total: R$ <span id="cart-total">0,00</span></strong>
                    <button class="btn btn-primary btn-sm" onclick="showCheckout()">
                        Finalizar Pedido
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let cart = [];

        function addToCart(type, id, name, price) {
            const existingItem = cart.find(item => item.type === type && item.id === id);

            if (existingItem) {
                existingItem.quantity++;
            } else {
                cart.push({
                    type: type,
                    id: id,
                    name: name,
                    price: price,
                    quantity: 1,
                    customizations: []
                });
            }

            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartSummary = document.getElementById('cart-summary');
            const cartItems = document.getElementById('cart-items');
            const cartTotal = document.getElementById('cart-total');

            if (cart.length === 0) {
                cartSummary.style.display = 'none';
                return;
            }

            cartSummary.style.display = 'block';

            let html = '';
            let total = 0;

            cart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;

                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small>${item.name}</small><br>
                            <small class="text-muted">${item.quantity}x R$ ${item.price.toFixed(2)}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            });

            cartItems.innerHTML = html;
            cartTotal.textContent = total.toFixed(2);
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
        }
    </script>
    @yield('scripts')
</body>
</html>
