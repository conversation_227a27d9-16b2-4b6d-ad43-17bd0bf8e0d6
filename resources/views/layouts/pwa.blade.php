<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', $establishment->name ?? 'Estabelecimento')</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />
    <meta name="theme-color" content="#1b0e0e">
    <link rel="manifest" href="/manifest.json">
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body {
            font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;
        }
    </style>
</head>
<body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#fcf8f8] justify-between group/design-root overflow-x-hidden">
        <div class="flex flex-col flex-1">
            <!-- Header -->
            <div class="flex items-center bg-[#fcf8f8] p-4 pb-2 justify-between shrink-0 border-b border-[#f3e7e8]">
                @if(isset($showBackButton) && $showBackButton)
                    <button onclick="history.back()" class="text-[#1b0e0e] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
                        </svg>
                    </button>
                @else
                    <div class="w-12"></div>
                @endif
                <h2 class="text-[#1b0e0e] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center">@yield('page-title', 'Menu')</h2>
                @if(isset($showCartButton) && $showCartButton)
                    <div class="flex w-12 items-center justify-end">
                        <button onclick="toggleCart()" class="flex max-w-[480px] cursor-pointer items-center justify-center rounded-xl h-12 bg-transparent text-[#1b0e0e] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0">
                            <div class="text-[#1b0e0e] relative" data-icon="ShoppingCart" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"></path>
                                </svg>
                                <span id="cart-badge" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                            </div>
                        </button>
                    </div>
                @else
                    <div class="w-12"></div>
                @endif
            </div>

            <!-- Main Content -->
            <main class="flex-1 flex flex-col min-h-0 pb-20">
                @yield('content')
            </main>
        </div>

        <!-- Bottom Navigation -->
        <div class="fixed bottom-0 left-0 right-0 z-40">
            <div class="flex gap-2 border-t border-[#f3e7e8] bg-[#fcf8f8] px-4 pb-3 pt-2">
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full {{ request()->routeIs('customer.pwa.menu') ? 'text-[#1b0e0e]' : 'text-[#994d51]' }}" href="{{ route('customer.pwa.menu', [$establishment, $location]) }}">
                    <div class="flex h-8 items-center justify-center" data-icon="List" data-size="24px" data-weight="{{ request()->routeIs('customer.pwa.menu') ? 'fill' : 'regular' }}">
                        @if(request()->routeIs('customer.pwa.menu'))
                            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M224,120v16a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V120a8,8,0,0,1,8-8H216A8,8,0,0,1,224,120Zm-8,56H40a8,8,0,0,0-8,8v16a8,8,0,0,0,8,8H216a8,8,0,0,0,8-8V184A8,8,0,0,0,216,176Zm0-128H40a8,8,0,0,0-8,8V72a8,8,0,0,0,8,8H216a8,8,0,0,0,8-8V56A8,8,0,0,0,216,48Z"></path>
                            </svg>
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M40,128a8,8,0,0,1,8-8H208a8,8,0,0,1,0,16H48A8,8,0,0,1,40,128ZM48,72H208a8,8,0,0,0,0-16H48a8,8,0,0,0,0,16ZM208,184H48a8,8,0,0,0,0,16H208a8,8,0,0,0,0-16Z"></path>
                            </svg>
                        @endif
                    </div>
                    <p class="text-xs font-medium leading-normal tracking-[0.015em]">Menu</p>
                </a>
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 {{ request()->routeIs('customer.pwa.cart') ? 'text-[#1b0e0e]' : 'text-[#994d51]' }}" href="{{ route('customer.pwa.cart', [$establishment, $location]) }}">
                    <div class="flex h-8 items-center justify-center" data-icon="ShoppingCart" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium leading-normal tracking-[0.015em]">Carrinho</p>
                </a>
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 {{ request()->routeIs('customer.pwa.qr-scanner') ? 'text-[#1b0e0e]' : 'text-[#994d51]' }}" href="{{ route('customer.pwa.qr-scanner', [$establishment, $location]) }}">
                    <div class="flex h-8 items-center justify-center" data-icon="Receipt" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M72,104a8,8,0,0,1,8-8h96a8,8,0,0,1,0,16H80A8,8,0,0,1,72,104Zm8,40h96a8,8,0,0,0,0-16H80a8,8,0,0,0,0,16ZM232,56V208a8,8,0,0,1-11.58,7.15L192,200.94l-28.42,14.21a8,8,0,0,1-7.16,0L128,200.94,99.58,215.15a8,8,0,0,1-7.16,0L64,200.94,35.58,215.15A8,8,0,0,1,24,208V56A16,16,0,0,1,40,40H216A16,16,0,0,1,232,56Zm-16,0H40V195.06l20.42-10.22a8,8,0,0,1,7.16,0L96,199.06l28.42-14.22a8,8,0,0,1,7.16,0L160,199.06l28.42-14.22a8,8,0,0,1,7.16,0L216,195.06Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium leading-normal tracking-[0.015em]">QR Code</p>
                </a>
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 {{ request()->routeIs('customer.pwa.chat') ? 'text-[#1b0e0e]' : 'text-[#994d51]' }}" href="{{ route('customer.pwa.chat', [$establishment, $location]) }}">
                    <div class="flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
                        </svg>
                    </div>
                    <p class="text-xs font-medium leading-normal tracking-[0.015em]">Assistente</p>
                </a>
            </div>
        </div>
    </div>

    <!-- Cart Modal -->
    <div id="cart-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="fixed bottom-0 left-0 right-0 bg-[#fcf8f8] rounded-t-xl max-h-[80vh] overflow-y-auto">
            <div class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-[#1b0e0e] text-lg font-bold">Seu Carrinho</h3>
                    <button onclick="toggleCart()" class="text-[#994d51]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"></path>
                        </svg>
                    </button>
                </div>
                <div id="cart-items-list">
                    <!-- Cart items will be populated here -->
                </div>
                <div class="border-t border-[#f3e7e8] pt-4 mt-4">
                    <div class="flex justify-between items-center mb-4">
                        <span class="text-[#1b0e0e] font-bold text-lg">Total: R$ <span id="cart-total">0,00</span></span>
                    </div>
                    <button onclick="proceedToCheckout()" class="w-full bg-[#1b0e0e] text-white py-3 rounded-xl font-medium">
                        Finalizar Pedido
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Geolocation Validation Overlay -->
    <div id="geo-validation-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-[#fcf8f8] rounded-xl max-w-sm w-full p-6 text-center">
            <div class="text-[#994d51] mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="currentColor" viewBox="0 0 256 256" class="mx-auto">
                    <path d="M128,64a40,40,0,1,0,40,40A40,40,0,0,0,128,64Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,128ZM176,16H80A16,16,0,0,0,64,32V224a16,16,0,0,0,16,16h96a16,16,0,0,0,16-16V32A16,16,0,0,0,176,16ZM80,32h96V224H80Z"></path>
                </svg>
            </div>
            <h3 class="text-[#1b0e0e] text-lg font-bold mb-2" id="geo-overlay-title">Localização necessária</h3>
            <p class="text-[#994d51] text-sm mb-6" id="geo-error-message">
                Para usar o sistema de pedidos, você precisa estar próximo ao estabelecimento.
            </p>
            <div class="flex flex-col gap-3">
                <button
                    onclick="retryGeoValidation()"
                    class="w-full bg-[#1b0e0e] text-white py-3 rounded-xl font-medium"
                    id="retry-geo-button"
                >
                    Tentar novamente
                </button>
                <button
                    onclick="hideGeoOverlay()"
                    class="w-full bg-[#f3e7e8] text-[#994d51] py-3 rounded-xl font-medium"
                >
                    Fechar
                </button>
            </div>
        </div>
    </div>

    <!-- Base JavaScript -->
    <script>
        // Load cart from localStorage or initialize empty
        let cart = JSON.parse(localStorage.getItem('ichabod_cart')) || [];

        function saveCart() {
            localStorage.setItem('ichabod_cart', JSON.stringify(cart));
        }

        function updateCartDisplay() {
            const badge = document.getElementById('cart-badge');
            const cartItemsList = document.getElementById('cart-items-list');
            const cartTotal = document.getElementById('cart-total');

            if (cart.length === 0) {
                badge.classList.add('hidden');
                if (cartItemsList) {
                    cartItemsList.innerHTML = '<p class="text-[#994d51] text-center py-8">Seu carrinho está vazio</p>';
                }
                if (cartTotal) {
                    cartTotal.textContent = '0,00';
                }
                return;
            }

            badge.classList.remove('hidden');
            badge.textContent = cart.reduce((sum, item) => sum + item.quantity, 0);

            let total = 0;
            let html = '';

            cart.forEach((item, index) => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;

                html += `
                    <div class="flex gap-4 bg-[#fcf8f8] py-3 border-b border-[#f3e7e8]">
                        <div class="flex flex-1 flex-col justify-center">
                            <p class="text-[#1b0e0e] text-base font-medium leading-normal">${item.name}</p>
                            <p class="text-[#994d51] text-sm font-normal leading-normal">R$ ${item.price.toFixed(2)} x ${item.quantity}</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <button onclick="updateQuantity(${index}, -1)" class="w-8 h-8 rounded-full bg-[#f3e7e8] flex items-center justify-center text-[#1b0e0e]">-</button>
                            <span class="text-[#1b0e0e] font-medium">${item.quantity}</span>
                            <button onclick="updateQuantity(${index}, 1)" class="w-8 h-8 rounded-full bg-[#1b0e0e] flex items-center justify-center text-white">+</button>
                        </div>
                    </div>
                `;
            });

            if (cartItemsList) {
                cartItemsList.innerHTML = html;
            }
            if (cartTotal) {
                cartTotal.textContent = total.toFixed(2).replace('.', ',');
            }
        }

        function addToCart(type, id, name, price) {
            const existingItem = cart.find(item => item.type === type && item.id === id);

            if (existingItem) {
                existingItem.quantity++;
            } else {
                cart.push({
                    type: type,
                    id: id,
                    name: name,
                    price: price,
                    quantity: 1
                });
            }

            updateCartDisplay();
        }

        function updateQuantity(index, change) {
            cart[index].quantity += change;
            if (cart[index].quantity <= 0) {
                cart.splice(index, 1);
            }
            updateCartDisplay();
        }

        function toggleCart() {
            const modal = document.getElementById('cart-modal');
            modal.classList.toggle('hidden');
        }

        function proceedToCheckout() {
            window.location.href = '{{ route("customer.pwa.cart", [$establishment ?? "establishment", $location ?? "location"]) }}';
        }

        // Initialize cart display
        document.addEventListener('DOMContentLoaded', function() {
            updateCartDisplay();

            // Register service worker for PWA
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            }

            // PWA install prompt
            let deferredPrompt;
            window.addEventListener('beforeinstallprompt', (e) => {
                // Prevent Chrome 67 and earlier from automatically showing the prompt
                e.preventDefault();
                // Stash the event so it can be triggered later
                deferredPrompt = e;

                // Show install button/banner (optional)
                showInstallPromotion();
            });

            // Handle app installed
            window.addEventListener('appinstalled', (evt) => {
                console.log('PWA was installed');
                hideInstallPromotion();
            });
        });

        // Show install promotion (optional)
        function showInstallPromotion() {
            // You can show a custom install banner here
            console.log('PWA can be installed');
        }

        // Hide install promotion
        function hideInstallPromotion() {
            // Hide custom install banner
            console.log('PWA install promotion hidden');
        }

        // Trigger PWA install
        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    } else {
                        console.log('User dismissed the install prompt');
                    }
                    deferredPrompt = null;
                });
            }
        }
    </script>

    <!-- Global PWA Session Management -->
    <script>
        // Global session management functions
        window.getPWASessionToken = function() {
            const storedToken = sessionStorage.getItem('pwa_session_token');
            const storedExpiry = sessionStorage.getItem('pwa_session_expiry');

            if (storedToken && storedExpiry) {
                const expiryTime = new Date(storedExpiry);
                const now = new Date();

                // If session is still valid (with 1 minute buffer), return it
                if (expiryTime > new Date(now.getTime() + 60000)) {
                    return storedToken;
                }
            }

            return null;
        };

        window.refreshPWASessionIfNeeded = async function() {
            const currentToken = window.getPWASessionToken();
            if (currentToken) {
                return currentToken;
            }

            // If no valid token and we have establishment/location data, start new session
            if (window.establishmentId && window.locationId) {
                try {
                    if (!navigator.geolocation) {
                        throw new Error('Geolocalização não suportada pelo navegador');
                    }

                    const position = await new Promise((resolve, reject) => {
                        navigator.geolocation.getCurrentPosition(resolve, reject, {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 60000
                        });
                    });

                    const response = await fetch('/api/customer/session/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            establishment_id: window.establishmentId,
                            location_id: window.locationId,
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Erro ao renovar sessão');
                    }

                    const sessionData = await response.json();

                    // Store session data
                    sessionStorage.setItem('pwa_session_token', sessionData.session_token);
                    sessionStorage.setItem('pwa_session_expiry', sessionData.expires_at);

                    return sessionData.session_token;

                } catch (error) {
                    console.error('Error refreshing PWA session:', error);
                    return null;
                }
            }

            return null;
        };

        // Set global establishment and location IDs if available
        @if(isset($establishment) && isset($location))
            window.establishmentId = {{ $establishment->id }};
            window.locationId = {{ $location->id }};
        @endif
    </script>

    @yield('scripts')
</body>
</html>
