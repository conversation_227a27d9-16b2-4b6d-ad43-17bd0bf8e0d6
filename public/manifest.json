{"name": "Ichabod - Digital Menu & Services", "short_name": "Ichabod", "description": "Digital menu and services platform for restaurants, hotels and establishments", "start_url": "/", "display": "standalone", "background_color": "#fcf8f8", "theme_color": "#1b0e0e", "orientation": "portrait-primary", "scope": "/", "lang": "pt-BR", "dir": "ltr", "categories": ["food", "business", "productivity"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Scan QR Code", "short_name": "QR Scanner", "description": "Quickly scan a QR code to access menu", "url": "/qr-scanner", "icons": [{"src": "/icons/qr-icon-96x96.png", "sizes": "96x96"}]}, {"name": "View Menu", "short_name": "<PERSON><PERSON>", "description": "Browse available dishes and services", "url": "/menu", "icons": [{"src": "/icons/menu-icon-96x96.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/mobile-menu.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Mobile menu view"}, {"src": "/screenshots/mobile-cart.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Mobile cart view"}, {"src": "/screenshots/desktop-menu.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Desktop menu view"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "protocol_handlers": [{"protocol": "web+ichabod", "url": "/c/%s"}]}