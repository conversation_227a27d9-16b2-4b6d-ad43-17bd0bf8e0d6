# Sistema de Gestão Multi-Estabelecimento com PWA

## 📋 Visão Geral

Sistema completo de gestão para estabelecimentos (hotéis, restaurantes, cafés, bares, food trucks) com PWA para clientes e painéis administrativos avançados. O sistema oferece funcionalidades de pedidos via QR Code, geovalidação, analytics avançado e notificações.

## 🏗️ Arquitetura do Sistema

### 🎯 **Níveis de Acesso**

1. **Super Administrador**
   - Gestão completa de todos os estabelecimentos
   - Criação e gerenciamento de usuários
   - Analytics globais
   - Configurações do sistema

2. **Administrador do Estabelecimento**
   - Gestão completa do próprio estabelecimento
   - Gerenciamento de pedidos, cardápio e locais
   - Analytics específicos
   - Configurações e notificações

3. **Cliente/Hóspede (PWA)**
   - Acesso via QR Code
   - Navegação do cardápio
   - Realização de pedidos
   - Acompanhamento de status

## 🚀 Funcionalidades Principais

### 📱 **PWA (Progressive Web App)**
- **Acesso via QR Code**: Escaneamento para acesso direto ao menu
- **Geovalidação**: Verificação automática de proximidade ao estabelecimento
- **Interface Responsiva**: Otimizada para dispositivos móveis
- **Carrinho Inteligente**: Adição/remoção de itens com validação
- **Status em Tempo Real**: Acompanhamento do pedido

### 🏨 **Gestão de Estabelecimentos**
- **Multi-tenant**: Suporte a múltiplos tipos de estabelecimento
- **Configurações Flexíveis**: Personalização por estabelecimento
- **Geoposicionamento**: Configuração de coordenadas e raio de entrega
- **Temas Personalizados**: Cores e logos customizáveis

### 📍 **Sistema de Locais**
- **Quartos/Mesas**: Gestão de locais por tipo de estabelecimento
- **QR Codes Únicos**: Geração automática para cada local
- **Status Dinâmico**: Disponível, ocupado, manutenção, reservado
- **Informações Detalhadas**: Capacidade, andar, seção

### 🍽️ **Gestão de Cardápio**
- **Pratos Detalhados**: Nome, descrição, preço, categoria
- **Ingredientes**: Lista completa com badges visuais
- **Restrições Alimentares**: Vegetariano, vegano, sem glúten, etc.
- **Customizações**: Opções que o cliente pode escolher
- **Imagens**: Suporte a URLs de imagens
- **Disponibilidade**: Ativação/desativação dinâmica

### 🛎️ **Gestão de Serviços** (Hotéis)
- **Serviços Variados**: Limpeza, room service, spa, etc.
- **Categorização**: Organização por tipo de serviço
- **Preços Flexíveis**: Serviços pagos ou gratuitos
- **Duração**: Tempo estimado de execução

### 📊 **Analytics Avançado**
- **Métricas Principais**: Receita total, ticket médio, total de pedidos
- **Gráficos Interativos**: Receita por dia, status de pedidos, receita por hora
- **Top Produtos**: Pratos mais vendidos com quantidades e receita
- **Análise de Locais**: Quartos/mesas mais utilizados
- **Exportação**: Funcionalidades de impressão e exportação

### 🔔 **Sistema de Notificações**
- **Notificações Personalizadas**: Mensagens específicas por pedido
- **Interface Intuitiva**: Modal para composição de mensagens
- **Base Extensível**: Preparado para SMS, WhatsApp, email

### 📍 **Geovalidação Avançada**
- **Configuração de Raio**: Administrador define distância máxima (10m-5km)
- **Validação em Tempo Real**: Verificação automática da localização do cliente
- **Feedback Visual**: Modais informativos sobre status da validação
- **Cálculo Preciso**: Fórmula de Haversine para distâncias

### ⚙️ **Configurações Avançadas**
- **Localização**: Coordenadas GPS e raio de entrega
- **Personalização**: Cores, logos, tempo de entrega
- **Automação**: Aceitação automática de pedidos
- **Notificações**: Habilitação/desabilitação do sistema

## 🗄️ **Estrutura do Banco de Dados**

### **Tabelas Principais**

#### `establishments`
- `id`, `name`, `type`, `description`
- `address`, `phone`, `email`, `logo_url`
- `settings` (JSON), `active`

#### `locations`
- `id`, `establishment_id`, `identifier`, `name`
- `type`, `capacity`, `status`, `qr_code`
- `customer_name`, `occupied_at`, `available_at`
- `floor`, `section`

#### `dishes`
- `id`, `establishment_id`, `name`, `description`
- `price`, `category`, `ingredients` (JSON)
- `dietary_restrictions` (JSON), `customizable_options` (JSON)
- `image_url`, `available`

#### `services`
- `id`, `establishment_id`, `name`, `description`
- `category`, `price`, `duration`, `available`

#### `orders`
- `id`, `establishment_id`, `location_id`
- `customer_name`, `status`, `total_amount`
- `special_requests`, `estimated_delivery`

#### `order_items`
- `id`, `order_id`, `dish_id`, `service_id`
- `quantity`, `unit_price`, `customizations` (JSON)
- `notes`

#### `users`
- `id`, `name`, `email`, `password`, `role`

#### `establishment_user` (Pivot)
- `establishment_id`, `user_id`, `role`

## 🛠️ **Tecnologias Utilizadas**

### **Backend**
- **Laravel 10+**: Framework PHP robusto
- **MySQL**: Banco de dados relacional
- **Eloquent ORM**: Mapeamento objeto-relacional

### **Frontend**
- **Bootstrap 5**: Framework CSS responsivo
- **Chart.js**: Gráficos interativos
- **QR Code Generator**: Geração de QR codes
- **Font Awesome**: Ícones

### **PWA**
- **Service Workers**: Cache e funcionalidade offline
- **Web App Manifest**: Instalação como app
- **Geolocation API**: Validação de localização

## 📁 **Estrutura de Arquivos**

\`\`\`
app/
├── Http/Controllers/
│   ├── CustomerController.php      # PWA e clientes
│   ├── EstablishmentController.php # Admin estabelecimento
│   └── SuperAdminController.php    # Super admin
├── Models/
│   ├── Establishment.php
│   ├── Location.php
│   ├── Dish.php
│   ├── Service.php
│   ├── Order.php
│   ├── OrderItem.php
│   └── User.php
└── ...

resources/views/
├── layouts/
│   ├── customer.blade.php          # Layout PWA
│   ├── establishment.blade.php     # Layout admin estabelecimento
│   └── super-admin.blade.php       # Layout super admin
├── customer/
│   ├── menu.blade.php              # Menu PWA
│   └── order-status.blade.php      # Status do pedido
├── establishment/
│   ├── dashboard.blade.php         # Dashboard
│   ├── analytics.blade.php         # Analytics avançado
│   ├── settings.blade.php          # Configurações
│   ├── orders/                     # Gestão de pedidos
│   ├── locations/                  # Gestão de locais
│   ├── dishes/                     # Gestão de cardápio
│   └── services/                   # Gestão de serviços
└── super-admin/                    # Painéis super admin
\`\`\`

## 🔐 **Sistema de Segurança**

### **Autenticação e Autorização**
- **Middleware de Acesso**: Verificação automática de permissões
- **Isolamento de Dados**: Cada estabelecimento vê apenas seus dados
- **Roles Hierárquicos**: Super admin > Admin > Usuário

### **Validação de Dados**
- **Validação Rigorosa**: Todas as entradas são validadas
- **Sanitização**: Prevenção de XSS e SQL injection
- **CSRF Protection**: Proteção contra ataques CSRF

## 🚀 **Instalação e Configuração**

### **Pré-requisitos**
- PHP 8.1+
- Composer
- MySQL 8.0+
- Node.js (para assets)

### **Passos de Instalação**

1. **Clone o repositório**
\`\`\`bash
git clone <repository-url>
cd laravel-management-system
\`\`\`

2. **Instale dependências**
\`\`\`bash
composer install
npm install
\`\`\`

3. **Configure ambiente**
\`\`\`bash
cp .env.example .env
php artisan key:generate
\`\`\`

4. **Configure banco de dados**
\`\`\`env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=management_system
DB_USERNAME=root
DB_PASSWORD=
\`\`\`

5. **Execute migrações**
\`\`\`bash
php artisan migrate
\`\`\`

6. **Compile assets**
\`\`\`bash
npm run build
\`\`\`

7. **Inicie servidor**
\`\`\`bash
php artisan serve
\`\`\`

## 📱 **Uso do Sistema**

### **Para Super Administradores**
1. Acesse `/super-admin/dashboard`
2. Crie estabelecimentos e usuários
3. Monitore analytics globais
4. Gerencie configurações do sistema

### **Para Administradores de Estabelecimento**
1. Faça login e seja redirecionado automaticamente
2. Configure seu estabelecimento em "Configurações"
3. Adicione locais (quartos/mesas) e gere QR codes
4. Cadastre pratos e serviços no cardápio
5. Monitore pedidos e envie notificações

### **Para Clientes (PWA)**
1. Escaneie o QR code do local
2. Permita acesso à localização
3. Navegue pelo cardápio
4. Adicione itens ao carrinho
5. Finalize o pedido
6. Acompanhe o status

## 🔧 **Configurações Avançadas**

### **Geovalidação**
- Configure coordenadas GPS do estabelecimento
- Defina raio de entrega (10m a 5km)
- Ative/desative validação por estabelecimento

### **Personalização**
- Upload de logos personalizados
- Configuração de cores do tema
- Tempo de entrega estimado
- Mensagens personalizadas

### **Notificações**
- Configure provedores de SMS/WhatsApp
- Personalize templates de mensagem
- Ative notificações automáticas

## 📈 **Roadmap Futuro**

### **Próximas Funcionalidades**
- [ ] Sistema de autenticação completo (Laravel Breeze)
- [ ] Notificações push em tempo real (WebSockets)
- [ ] Exportação de relatórios (PDF/Excel)
- [ ] Cache Redis para performance
- [ ] Testes automatizados
- [ ] API REST completa
- [ ] Integração com gateways de pagamento
- [ ] Sistema de avaliações e comentários
- [ ] Programa de fidelidade
- [ ] Integração com delivery

## 🤝 **Contribuição**

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 **Licença**

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE.md](LICENSE.md) para detalhes.

## 📞 **Suporte**

Para suporte técnico ou dúvidas:
- Abra uma issue no GitHub
- Entre em contato via email
- Consulte a documentação completa

---

**Desenvolvido com ❤️ para revolucionar a gestão de estabelecimentos**
