# Colors
GREEN=\033[0;32m
YELLOW=\033[0;33m
RESET=\033[0m

# Get the arguments after the command
%:
	@:

# Variables
DOCKER_COMPOSE=docker-compose
ARTISAN=$(DOCKER_COMPOSE) exec app php artisan
COMPOSER=$(DOCKER_COMPOSE) exec app composer
NPM=$(DOCKER_COMPOSE) exec app npm

.PHONY: up down restart artisan migrate fresh seed tinker test composer npm dev

##
## Environment Management
##---------------------------------------------------------------------------
up:
	@echo "$(YELLOW)Starting up services...$(RESET)"
	@$(DOCKER_COMPOSE) up -d --build
	@echo "$(GREEN)Services started.$(RESET)"

down:
	@echo "$(YELLOW)Stopping services...$(RESET)"
	@$(DOCKER_COMPOSE) down
	@echo "$(GREEN)Services stopped.$(RESET)"

restart: down up

##
## Laravel Artisan & Composer
##---------------------------------------------------------------------------
artisan:
	@$(ARTISAN) $(filter-out $@,$(MAKECMDGOALS))

migrate:
	@echo "$(YELLOW)Running migrations...$(RESET)"
	@$(ARTISAN) migrate

fresh:
	@echo "$(YELLOW)Running fresh migrations with seed...$(RESET)"
	@$(ARTISAN) migrate:fresh --seed

seed:
	@echo "$(YELLOW)Seeding database...$(RESET)"
	@$(ARTISAN) db:seed

tinker:
	@$(ARTISAN) tinker

test:
	@echo "$(YELLOW)Running tests...$(RESET)"
	@$(ARTISAN) test $(filter-out $@,$(MAKECMDGOALS))

composer:
	@$(COMPOSER) $(filter-out $@,$(MAKECMDGOALS))

##
## Frontend
##---------------------------------------------------------------------------
npm:
	@$(NPM) $(filter-out $@,$(MAKECMDGOALS))

dev:
	@echo "$(YELLOW)Starting Vite dev server...$(RESET)"
	@$(NPM) run dev

##
## Help
##---------------------------------------------------------------------------
help:
	@echo ''
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Environment Management:'
	@echo '  ${YELLOW}up${RESET}              - Start up services'
	@echo '  ${YELLOW}down${RESET}            - Stop services'
	@echo '  ${YELLOW}restart${RESET}         - Restart services'
	@echo ''
	@echo 'Laravel Artisan & Composer:'
	@echo '  ${YELLOW}artisan <command>${RESET} - Run an Artisan command'
	@echo '  ${YELLOW}migrate${RESET}         - Run database migrations'
	@echo '  ${YELLOW}fresh${RESET}           - Run fresh migrations with seed'
	@echo '  ${YELLOW}seed${RESET}            - Seed the database'
	@echo '  ${YELLOW}tinker${RESET}          - Start Tinker session'
	@echo '  ${YELLOW}test${RESET}            - Run tests'
	@echo '  ${YELLOW}composer <command>${RESET}- Run a Composer command'
	@echo ''
	@echo 'Frontend:'
	@echo '  ${YELLOW}npm <command>${RESET}    - Run an npm command'
	@echo '  ${YELLOW}dev${RESET}             - Start Vite dev server'
	@echo '' 