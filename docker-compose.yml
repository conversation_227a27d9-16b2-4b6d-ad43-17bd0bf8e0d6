version: '3.8'

services:
  app:
    build:
      args:
        user: nicolas
        uid: 1000
      context: ./
      dockerfile: containers/php/Dockerfile
    image: ichabod-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
    networks:
      - ichabod

  vite:
    image: node:lts-alpine
    user: "1000:1000"
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
    ports:
      - "5173:5173"
    networks:
      - ichabod
    command: sh -c "npm install && npm run dev"

  web:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./:/var/www/html
      - ./containers/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - ichabod

  db:
    image: mysql:8.0
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
    volumes:
      - ./storage/mysql:/var/lib/mysql
    networks:
      - ichabod

networks:
  ichabod:
    driver: bridge
